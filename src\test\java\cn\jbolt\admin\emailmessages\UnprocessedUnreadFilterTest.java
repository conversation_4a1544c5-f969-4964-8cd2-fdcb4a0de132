package cn.jbolt.admin.emailmessages;

import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.Kv;
import cn.jbolt.core.model.Ret;
import cn.jbolt.test.InitEnv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

/**
 * 未处理和未读邮件筛选功能测试
 * 测试新增的"未处理邮件"和"未读邮件"筛选选项
 */
public class UnprocessedUnreadFilterTest {

    private static EmailMessagesService emailMessagesService;
    private static final Long TEST_USER_ID = 1L;

    public static void main(String[] args) {
        InitEnv.initEnvironment();
        emailMessagesService = new EmailMessagesService();

        System.out.println("=== Unprocessed and Unread Email Filter Test ===");

        // Set test user
        JBoltUserKit.setUserId(TEST_USER_ID);

        // Prepare test data
        prepareTestData();

        // Run tests
        testUnprocessedEmailFilter();
        testUnreadEmailFilter();
        testCombinedFilters();
        testExistingFiltersStillWork();

        // Clean test data
        cleanTestData();

        System.out.println("=== Test Complete ===");
    }

    /**
     * Prepare test data
     */
    public static void prepareTestData() {
        System.out.println("Preparing test data...");
        
        // Clean existing test data
        cleanTestData();
        
        // Insert test email data
        String insertSql = "INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, " +
                          "from_display, from_address, to_display, to_address, folder_name, sent_date, created_at, " +
                          "is_read, is_follow_up, is_delete) VALUES " +
                          "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?, 0)";
        
        // Test email 1: Unprocessed and unread
        Db.update(insertSql, 9999201L, "<EMAIL>", "test-msg-201", "hash201", "New Customer Inquiry",
                 "Customer A", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 0, 0); // is_read=0, is_follow_up=0
        
        // Test email 2: Processed but unread
        Db.update(insertSql, 9999202L, "<EMAIL>", "test-msg-202", "hash202", "Meeting Reminder",
                 "John Smith", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 0, 1); // is_read=0, is_follow_up=1
        
        // Test email 3: Unprocessed but read
        Db.update(insertSql, 9999203L, "<EMAIL>", "test-msg-203", "hash203", "Project Update",
                 "Jane Doe", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 1, 0); // is_read=1, is_follow_up=0
        
        // Test email 4: Processed and read
        Db.update(insertSql, 9999204L, "<EMAIL>", "test-msg-204", "hash204", "Weekly Report",
                 "Bob Wilson", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 1, 1); // is_read=1, is_follow_up=1
        
        // Test email 5: NULL values (should be treated as unprocessed/unread)
        Db.update("INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, " +
                  "from_display, from_address, to_display, to_address, folder_name, sent_date, created_at, " +
                  "is_read, is_follow_up, is_delete) VALUES " +
                  "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NULL, NULL, 0)",
                  9999205L, "<EMAIL>", "test-msg-205", "hash205", "System Notification",
                  "System", "<EMAIL>", "Test User", "<EMAIL>", "INBOX");
        
        System.out.println("Test data preparation complete");
    }

    /**
     * Clean test data
     */
    public static void cleanTestData() {
        Db.update("DELETE FROM email_messages WHERE id BETWEEN 9999201 AND 9999299");
    }

    /**
     * Test unprocessed email filter
     */
    public static void testUnprocessedEmailFilter() {
        System.out.println("\nTesting unprocessed email filter...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("folderFilter", "unprocessed")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find emails with is_follow_up = 0 or NULL (emails 1, 3, 5)
            long unprocessedCount = emails.stream().filter(email -> {
                Integer isFollowUp = email.getInt("is_follow_up");
                return isFollowUp == null || isFollowUp == 0;
            }).count();
            
            if (unprocessedCount >= 3) {
                System.out.println("PASS - Unprocessed email filter test passed, found " + unprocessedCount + " unprocessed emails");
            } else {
                System.out.println("FAIL - Unprocessed email filter test failed, expected at least 3 emails, found " + unprocessedCount);
            }
        } else {
            System.out.println("FAIL - Unprocessed email filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test unread email filter
     */
    public static void testUnreadEmailFilter() {
        System.out.println("\nTesting unread email filter...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("folderFilter", "unread")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find emails with is_read = 0 or NULL (emails 1, 2, 5)
            long unreadCount = emails.stream().filter(email -> {
                Integer isRead = email.getInt("is_read");
                return isRead == null || isRead == 0;
            }).count();
            
            if (unreadCount >= 3) {
                System.out.println("PASS - Unread email filter test passed, found " + unreadCount + " unread emails");
            } else {
                System.out.println("FAIL - Unread email filter test failed, expected at least 3 emails, found " + unreadCount);
            }
        } else {
            System.out.println("FAIL - Unread email filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test combined filters
     */
    public static void testCombinedFilters() {
        System.out.println("\nTesting combined unprocessed and subject filters...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("folderFilter", "unprocessed")
                      .set("subjectFilter", "Customer")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find only email 1 which is unprocessed AND has "Customer" in subject
            boolean foundTargetEmail = emails.stream().anyMatch(email -> {
                String subject = email.getStr("subject");
                Integer isFollowUp = email.getInt("is_follow_up");
                
                boolean isUnprocessed = isFollowUp == null || isFollowUp == 0;
                boolean hasCustomerInSubject = subject != null && subject.contains("Customer");
                
                return isUnprocessed && hasCustomerInSubject;
            });
            
            if (foundTargetEmail) {
                System.out.println("PASS - Combined filters test passed, found email matching both criteria");
            } else {
                System.out.println("FAIL - Combined filters test failed, no email found matching both criteria");
            }
        } else {
            System.out.println("FAIL - Combined filters test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test that existing filters still work
     */
    public static void testExistingFiltersStillWork() {
        System.out.println("\nTesting that existing inbox filter still works...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("folderFilter", "inbox")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find all test emails since they're all in INBOX
            if (emails.size() >= 5) {
                System.out.println("PASS - Existing inbox filter test passed, found " + emails.size() + " emails in inbox");
            } else {
                System.out.println("FAIL - Existing inbox filter test failed, expected at least 5 emails, found " + emails.size());
            }
        } else {
            System.out.println("FAIL - Existing inbox filter test failed: " + result.getStr("msg"));
        }
    }
}
