# 邮件历史权限控制功能

## 功能概述

在 `EmailMessagesService.getEmailHistory()` 方法中实现了邮件历史查询的权限控制，确保用户只能查看有权限的邮件内容。

## 权限控制逻辑

### 1. 用户管理的邮箱账号
- **判断条件**: 查询的邮箱在 `user_email` 表中，且 `user_id` 为当前登录用户
- **权限范围**: 可以查看该邮箱的**所有来往邮件**
- **SQL查询**: 
  ```sql
  WHERE (s.from_address = ? OR s.to_address LIKE ? OR s.cc_address LIKE ?)
  ```

### 2. 外部邮箱账号
- **判断条件**: 查询的邮箱不在当前用户的 `user_email` 表中
- **权限范围**: 只能查看该邮箱与**用户管理的邮箱账号**之间的来往邮件
- **SQL查询**:
  ```sql
  WHERE ((s.from_address = ? AND s.to_address IN (SELECT email FROM user_email WHERE user_id = ?)) 
      OR (s.from_address IN (SELECT email FROM user_email WHERE user_id = ?) AND s.to_address LIKE ?) 
      OR (s.from_address = ? AND s.cc_address IN (SELECT email FROM user_email WHERE user_id = ?)) 
      OR (s.from_address IN (SELECT email FROM user_email WHERE user_id = ?) AND s.cc_address LIKE ?))
  ```

## 实现细节

### 权限检查流程
1. 获取当前登录用户ID (`JBoltUserKit.getUserId()`)
2. 验证用户是否已登录
3. 查询 `user_email` 表判断邮箱是否为用户管理
4. 根据权限类型构建不同的SQL查询条件

### 关键代码位置
- **文件**: `src/main/java/cn/jbolt/admin/emailmessages/EmailMessagesService.java`
- **方法**: `getEmailHistory(int page, int pageSize, Kv params)`
- **行数**: 132-193

### 数据库表依赖
- `user_email`: 用户邮箱关联表
  - `user_id`: 用户ID
  - `email`: 邮箱地址
- `email_messages`: 邮件消息表
  - `from_address`: 发件人邮箱
  - `to_address`: 收件人邮箱
  - `cc_address`: 抄送邮箱

## 安全特性

### 1. 用户身份验证
- 必须有有效的登录用户ID
- 未登录用户会被拒绝访问

### 2. 数据隔离
- 用户只能查看与自己管理的邮箱相关的邮件
- 外部邮箱之间的邮件对用户不可见

### 3. 日志记录
- 记录权限检查结果
- 记录查询类型（完整权限 vs 限制权限）
- 便于审计和调试

## 测试验证

### 测试文件
`src/test/java/cn/jbolt/admin/emailmessages/EmailHistoryPermissionTest.java`

### 测试用例
1. **用户管理邮箱测试** - 验证完整权限查询
2. **外部邮箱测试** - 验证限制权限查询
3. **参数验证测试** - 验证空值和null值处理
4. **用户登录验证** - 验证未登录用户拒绝
5. **权限检查SQL** - 验证数据库查询逻辑

### 运行测试
```bash
mvn exec:java -Dexec.mainClass="cn.jbolt.admin.emailmessages.EmailHistoryPermissionTest" -Dexec.classpathScope=test
```

## 使用示例

### 查询用户管理的邮箱
```java
Kv params = Kv.by("email", "<EMAIL>");
Ret result = emailMessagesService.getEmailHistory(1, 10, params);
// 可以查看该邮箱的所有来往邮件
```

### 查询外部邮箱
```java
Kv params = Kv.by("email", "<EMAIL>");
Ret result = emailMessagesService.getEmailHistory(1, 10, params);
// 只能查看与用户管理邮箱的来往邮件
```

## 注意事项

1. **性能考虑**: 外部邮箱查询使用了子查询，在大数据量情况下可能需要优化
2. **索引建议**: 建议在 `user_email(user_id, email)` 上创建复合索引
3. **缓存优化**: 可以考虑缓存用户管理的邮箱列表以提高性能
4. **日志级别**: 生产环境可以调整日志级别以减少输出

## 版本信息
- **实现日期**: 2025-07-29
- **版本**: 1.0.0
- **作者**: Augment Agent
