package cn.jbolt.index;

import cn.hutool.core.io.FileUtil;
import cn.jbolt._admin.globalconfig.GlobalConfigService;
import cn.jbolt._admin.onlineuser.OnlineUserService;
import cn.jbolt._admin.topnav.TopnavService;
import cn.jbolt._admin.user.UserService;
import cn.jbolt.admin.company.CompanyService;
import cn.jbolt.admin.emails.EmailNameCache;
import cn.jbolt.admin.emailmessages.EmailMessagesService;
import cn.jbolt.common.model.Client;
import cn.jbolt.common.model.Company;
import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.core.base.JBoltGlobalConfigKey;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.base.config.JBoltConfig;
import cn.jbolt.core.cache.JBoltGlobalConfigCache;
import cn.jbolt.core.cache.JBoltPermissionCache;
import cn.jbolt.core.cache.JBoltUserConfigCache;
import cn.jbolt.core.common.enums.JBoltLoginState;
import cn.jbolt.core.consts.JBoltConst;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.handler.base.JBoltBaseHandler;
import cn.jbolt.core.kit.JBoltControllerKit;
import cn.jbolt.core.kit.JBoltSaasTenantKit;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.LoginLog;
import cn.jbolt.core.model.User;
import cn.jbolt.core.para.JBoltNoUrlPara;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt.core.permission.UnCheck;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import cn.jbolt.core.service.JBoltLoginLogUtil;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.core.JFinal;
import com.jfinal.kit.Kv;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.jbolt.common.util.AnnotationUtil.getTableName;

/**
 * 系统后台主入口
 *
 * @ClassName: AdminIndexController
 * @author: JFinal学院-小木 QQ：*********
 * @date: 2020年3月8日
 */
public class AdminIndexController extends JBoltBaseController {
    @Inject
    private UserService userService;
    @Inject
    private GlobalConfigService globalConfigService;
    @Inject
    private TopnavService topnavService;
    @Inject
    private OnlineUserService onlineUserService;
    @Inject
    private EmailNameCache emailNameCache;
    @Inject
    private CompanyService companyService;
    @Inject
    private EmailMessagesService emailMessagesService;

    @UnCheck
    @Before(JBoltNoUrlPara.class)
    public void index() {
        render("index.html");
    }

    @UnCheck
    public void menu() {
        set("leftMenus", JBoltPermissionCache.me.getRoleMenus(JBoltUserKit.getUserRoleIds()));
        render("menu.html");
    }

    @UnCheck
    public void lockSystem() {
        renderJson(onlineUserService.lockCurrentUserScreen());
    }

    @UnCheck
    public void unLockSystem() {
        String password = get("password");
        if (notOk(password)) {
            renderJsonFail("请输入登录密码");
        } else {
            boolean success = userService.checkPwd(JBoltUserKit.getUserId(), password);
            if (success) {
                renderJson(onlineUserService.unlockCurrentUserScreen());
            } else {
                renderJsonFail("密码不正确");
            }
        }

    }

    @CheckPermission("dashboard")
    @UnCheckIfSystemAdmin
    public void dashboard() {
        set("numberOfEmailAccount", Db.queryInt("select count(*) from " + getTableName(EmailAccount.class)));
        set("numberOfCompany", Db.queryInt("select count(*) from " + getTableName(Company.class)));
        set("numberOfClient", Db.queryInt("select count(*) from " + getTableName(Client.class)));
        set("numberOfEmail", Db.queryInt("select count(*) from " + getTableName(EmailMessages.class)));
        render("dashboard.html");
    }

    @UnCheckIfSystemAdmin
    public void getNumberOfEmail() {
        Integer number = Db.queryInt("select count(*) from " + getTableName(EmailMessages.class));
        renderJsonData(number);
    }

    /**
     * 邮件列表模板示例页面
     */
    @CheckPermission("dashboard")
    @UnCheckIfSystemAdmin
    public void emailListExample() {
        render("/_view/_common/email_list_example.html");
    }

    /**
     * 获取示例邮件数据（用于邮件列表模板演示）
     */
    @CheckPermission("dashboard")
    @UnCheckIfSystemAdmin
    public void getSampleEmails() {
        List<Kv> sampleEmails = new ArrayList<>();
        
        // 创建示例邮件数据
        sampleEmails.add(Kv.by("id", 1)
            .set("from_address", "<EMAIL>")
            .set("fromname", "John Doe")
            .set("to_address", "<EMAIL>")
            .set("toname", "系统管理员")
            .set("subject", "项目进度更新报告")
            .set("sentdate", "2024-01-15 10:30:00")
            .set("content_html", "<p>尊敬的管理员，</p><p>本周项目进展如下：<br/>1. 完成了前端界面设计<br/>2. 后端API开发进度50%<br/>3. 数据库设计已完成</p><p>请查收附件中的详细报告。</p>")
            .set("content_text", "尊敬的管理员，本周项目进展如下：1. 完成了前端界面设计 2. 后端API开发进度50% 3. 数据库设计已完成 请查收附件中的详细报告。")
            .set("is_read", false)
            .set("is_follow_up", false)
            .set("is_important", true)
            .set("has_attachments", true)
            .set("email_account", "<EMAIL>"));
            
        sampleEmails.add(Kv.by("id", 2)
            .set("from_address", "<EMAIL>")
            .set("fromname", "系统管理员")
            .set("to_address", "<EMAIL>")
            .set("toname", "张三")
            .set("subject", "会议安排通知")
            .set("sentdate", "2024-01-15 14:15:00")
            .set("content_html", "<p>张先生，</p><p>关于明天的产品演示会议：</p><ul><li>时间：2024年1月16日 下午2:00</li><li>地点：公司会议室A</li><li>议程：产品功能演示、需求确认</li></ul><p>请准时参加，谢谢！</p>")
            .set("content_text", "张先生，关于明天的产品演示会议：时间：2024年1月16日 下午2:00 地点：公司会议室A 议程：产品功能演示、需求确认 请准时参加，谢谢！")
            .set("is_read", true)
            .set("is_follow_up", true)
            .set("is_important", false)
            .set("has_attachments", false)
            .set("email_account", "<EMAIL>"));
            
        sampleEmails.add(Kv.by("id", 3)
            .set("from_address", "<EMAIL>")
            .set("fromname", "技术支持团队")
            .set("to_address", "<EMAIL>")
            .set("toname", "系统管理员")
            .set("subject", "系统维护通知")
            .set("sentdate", "2024-01-16 09:00:00")
            .set("content_html", "<p>尊敬的用户，</p><p>我们将在今晚进行系统维护：</p><p><strong>维护时间：</strong>2024年1月16日 23:00 - 次日 01:00</p><p><strong>影响范围：</strong>所有在线服务</p><p>维护期间系统将暂时无法访问，给您带来的不便敬请谅解。</p>")
            .set("content_text", "尊敬的用户，我们将在今晚进行系统维护：维护时间：2024年1月16日 23:00 - 次日 01:00 影响范围：所有在线服务 维护期间系统将暂时无法访问，给您带来的不便敬请谅解。")
            .set("is_read", false)
            .set("is_follow_up", false)
            .set("is_important", false)
            .set("has_attachments", false)
            .set("email_account", "<EMAIL>"));
            
        sampleEmails.add(Kv.by("id", 4)
            .set("from_address", "<EMAIL>")
            .set("fromname", "人事部")
            .set("to_address", "<EMAIL>")
            .set("toname", "全体员工")
            .set("subject", "春节放假通知")
            .set("sentdate", "2024-01-10 16:30:00")
            .set("content_html", "<p>各位同事，</p><p>根据国家法定节假日安排，公司春节放假时间如下：</p><p><strong>放假时间：</strong>2024年2月8日 - 2024年2月18日</p><p><strong>上班时间：</strong>2024年2月19日（正月初十）</p><p>祝大家春节快乐，身体健康！</p>")
            .set("content_text", "各位同事，根据国家法定节假日安排，公司春节放假时间如下：放假时间：2024年2月8日 - 2024年2月18日 上班时间：2024年2月19日（正月初十）祝大家春节快乐，身体健康！")
            .set("is_read", true)
            .set("is_follow_up", false)
            .set("is_important", true)
            .set("has_attachments", true)
            .set("email_account", "<EMAIL>"));
            
        sampleEmails.add(Kv.by("id", 5)
            .set("from_address", "<EMAIL>")
            .set("fromname", "市场部")
            .set("to_address", "<EMAIL>")
            .set("toname", "系统管理员")
            .set("subject", "合作方案讨论")
            .set("sentdate", "2024-01-14 11:20:00")
            .set("content_html", "<p>您好，</p><p>我们想就以下合作方案与贵公司进行深入讨论：</p><ol><li>产品渠道合作</li><li>技术资源共享</li><li>市场推广联合</li></ol><p>期待您的回复，以便安排详细会谈。</p>")
            .set("content_text", "您好，我们想就以下合作方案与贵公司进行深入讨论：1. 产品渠道合作 2. 技术资源共享 3. 市场推广联合 期待您的回复，以便安排详细会谈。")
            .set("is_read", false)
            .set("is_follow_up", true)
            .set("is_important", false)
            .set("has_attachments", true)
            .set("email_account", "<EMAIL>"));
            
        renderJsonData(sampleEmails);
    }


    @Clear
    public void relogin() {
        render("relogin.html");
    }

    /**
     * 手动操作退出系统
     */
    @Clear
    public void logout() {
        HttpSession session = getSession();
        if (session != null) {
            session.invalidate();
        }
        //当前用户自行离线
        onlineUserService.currentUserLogout();
        //删除cookie存的sessionId
        removeCookie(JBoltConst.JBOLT_SESSIONID_KEY);
        removeCookie(JBoltConst.JBOLT_SESSIONID_REFRESH_TOKEN);
        removeCookie(JBoltConst.JBOLT_KEEPLOGIN_KEY);
        redirect(JBoltBaseHandler.processBasePath(getRequest()));
    }

    /**
     * 进入一个异端顶替登录下线的提示界面
     */
    @Clear
    public void terminalOffline() {
        setMsg(JBoltMsg.ADMIN_TERMINAL_OFFLINE_PAGE_MSG);
        render("offline.html");
    }

    /**
     * 进入一个被强制下线的界面
     */
    @Clear
    public void forcedOffline() {
        setMsg(JBoltMsg.ADMIN_TERMINAL_OFFLINE_PAGE_MSG);
        render("offline.html");
    }

    /**
     * 登录
     */
    @Clear
    public void login() {
        //判断不是post就是进登录页面
        if (!getRequest().getMethod().equalsIgnoreCase("post")) {
            render(getLoginFileName());
            return;
        }
        //创建登录日志
        LoginLog log = JBoltLoginLogUtil.createLoginLog(getRequest());
        log.setUsername(get("username"));

        //根据全局配置判断是否需要验证码 默认需要
        boolean checkCaptcha = JBoltGlobalConfigCache.me.isJBoltLoginUseCapture();
        if (checkCaptcha) {
            boolean checkSuccess = validateCaptcha("captcha");
            if (!checkSuccess) {
                log.setLoginState(JBoltLoginState.CAPTURE_ERROR.getValue());
                log.save();
                renderJsonFail(JBoltLoginState.CAPTURE_ERROR.getText());
                return;
            }
        }

        Ret ret = userService.getUser(get("username"), get("password"));
        User user = ret.isFail() ? null : ret.getAs("data");
        //检测用户名密码是否正确输入并得到user
        if (user == null) {
            log.setLoginState(JBoltLoginState.USERNAME_PWD_ERROR.getValue());
            log.save();
            renderJson(ret);
            return;
        }

        log.setUserId(user.getId());
        //检测用户是否禁用
        if (user.getEnable() == null || !user.getEnable()) {
            log.setLoginState(JBoltLoginState.ENABLE_ERROR.getValue());
            log.save();
            renderJsonFail(JBoltLoginState.ENABLE_ERROR.getText());
            return;
        }

        //检测角色权限分配
        if (notOk(user.getRoles()) && (user.getIsSystemAdmin() == null || !user.getIsSystemAdmin())) {
            log.setLoginState(JBoltLoginState.NOT_ASSIGN_ROLE_ERROR.getValue());
            log.save();
            renderJsonFail(JBoltLoginState.NOT_ASSIGN_ROLE_ERROR.getText());
            return;
        }

        log.setLoginState(JBoltLoginState.LOGIN_SUCCESS.getValue());
        log.save();

        //处理用户登录信息 异地登录异常信息
        boolean isRemoteLogin = userService.processUserRemoteLogin(user, log);
        userService.processUserLoginInfo(user, isRemoteLogin, log);
        //登录后的处理
        afterLogin(user, log);
        renderJsonSuccess();
    }

    /**
     * 获取登录页文件名
     *
     * @return
     */
    private String getLoginFileName() {
        String fileName = JBoltGlobalConfigCache.me.getLoginFile();
        if (isOk(fileName)) {
            String filePath = PathKit.getWebRootPath() + "/" + "_view" + "/" + "_admin" + "/" + "index" + "/" + fileName;
            if (!FileUtil.exist(filePath)) {
                fileName = JBoltConst.JBOLT_ADMIN_LOGIN_DEFAULT_FILE;
            }
        } else {
            fileName = JBoltConst.JBOLT_ADMIN_LOGIN_DEFAULT_FILE;
        }
        return fileName;
    }

    /**
     * 登录后设置用户相关的登录cookie
     *
     * @param user
     * @param log
     */
    private void afterLogin(User user, LoginLog log) {
        //处理onlineUser
        boolean keeplogin = getCheckBoxBoolean("keepLogin");
        Kv result = onlineUserService.processUserLogin(keeplogin, user, log);
        if (result != null && !result.isEmpty()) {
            int keepLoginSeconds = result.getInt("keepLoginSeconds");
            String sessionId = result.getStr("sessionId");
            //设置时长根据参数配置中的值 如果没配置或者获取配置异常 就按照默认8小时
            setCookie(JBoltConst.JBOLT_SESSIONID_KEY, sessionId, keepLoginSeconds, JFinal.me().getContextPath(), true);
            //设置refreshToken jwt
            int refreshTokenLiveSeconds = keepLoginSeconds + 28800;
            setCookie(JBoltConst.JBOLT_SESSIONID_REFRESH_TOKEN, onlineUserService.genNewSessionIdRefreshToken(user, sessionId, refreshTokenLiveSeconds), refreshTokenLiveSeconds, JFinal.me().getContextPath(), true);
            //设置是否keepLogin
            setCookie(JBoltConst.JBOLT_KEEPLOGIN_KEY, keeplogin ? "true" : "false", refreshTokenLiveSeconds, JFinal.me().getContextPath(), true);
        }
        //设置用户样式配置的cookie
        resetUserConfigCookie(user.getId());
    }

    /**
     * 跳转到锁屏页面
     */
    public void redirectToScreenLock() {
        JBoltControllerKit.renderSystemLockedPage(this);
    }

    /**
     * 登录后重置登录页面用户设置cookie
     *
     * @param userId
     */
    private void resetUserConfigCookie(Long userId) {
        int seconds = 31536000;
        //365天不失效 cookie
//		boolean glass=JBoltUserConfigCache.me.getJBoltLoginFormStyleGlass(userId);
//		setCookie("jbolt_login_glassStyle",glass+"",seconds,JFinal.me().getContextPath());
        boolean blur = JBoltUserConfigCache.me.getJBoltLoginBgimgBlur(userId);
        setCookie("jbolt_login_bgimgBlur", blur + "", seconds, JFinal.me().getContextPath());
        boolean nest = JBoltUserConfigCache.me.getJBoltLoginNest(userId);
        setCookie("jbolt_login_nest", nest + "", seconds, JFinal.me().getContextPath());
    }

    /**
     * 验证码
     */
    @Clear
    public void captcha() {
        renderJBoltCaptcha(JBoltGlobalConfigCache.me.getConfigValue(JBoltGlobalConfigKey.JBOLT_LOGIN_CAPTURE_TYPE));
    }


    /**
     * 获取当前用户Cookie里的TOKEN-jboltid
     */
    @UnCheck
    public void myToken() {
        Kv kv = Kv.by("token", JBoltUserKit.getUserSessionId());
        if (JBoltConfig.SAAS_ENABLE && JBoltSaasTenantKit.me.isSelfRequest()) {
            kv.set("tenantSn", JBoltSaasTenantKit.me.getSn());
        }
        renderJsonData(kv);
    }

    /**
     * 获取最近邮件列表
     * 优化版本：限制返回数量，添加更多日志，优化查询，支持动态天数和自定义时间范围
     */
    @UnCheck
    public void getRecentEmails() {
        try {
            Boolean customerOnly = getBoolean("customerOnly", false);
            String startDateStr = getPara("startDate");
            String endDateStr = getPara("endDate");
            
            Date startDate;
            Date endDate;
            String timeRangeDesc;
            
            // 如果传入了自定义时间范围
            if (startDateStr != null && endDateStr != null) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    startDate = dateFormat.parse(startDateStr);
                    
                    // 结束日期需要包含当天的所有时间，所以设置为第二天的00:00:00
                    Calendar endCalendar = Calendar.getInstance();
                    endCalendar.setTime(dateFormat.parse(endDateStr));
                    endCalendar.add(Calendar.DAY_OF_YEAR, 1);
                    endDate = endCalendar.getTime();
                    
                    timeRangeDesc = "自定义时间范围：" + startDateStr + " 至 " + endDateStr;
                    
                    // 验证时间范围合理性
                    if (startDate.after(new Date())) {
                        renderJsonFail("开始日期不能是未来时间");
                        return;
                    }
                    
                    if (startDate.after(endDate)) {
                        renderJsonFail("开始日期不能晚于结束日期");
                        return;
                    }
                    
                    // 限制查询范围不超过1年
                    Calendar oneYearAgo = Calendar.getInstance();
                    oneYearAgo.add(Calendar.YEAR, -1);
                    if (startDate.before(oneYearAgo.getTime())) {
                        renderJsonFail("查询时间范围不能超过一年");
                        return;
                    }
                    
                } catch (Exception e) {
                    renderJsonFail("日期格式错误，请使用 yyyy-MM-dd 格式");
                    return;
                }
            } else {
                // 使用天数参数，默认为1天
                Integer days = getInt("days", 1);
                
                // 参数校验：限制最大天数为30天
                if (days == null || days < 1) {
                    days = 1;
                }
                if (days > 30) {
                    days = 30;
                }
                
                // 根据传入的天数参数计算日期
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, -days);
                startDate = calendar.getTime();
                endDate = new Date(); // 结束日期为当前时间
                
                timeRangeDesc = "最近" + days + "天";
            }
            Long userId = JBoltUserKit.getUserId();

            // 记录开始时间
            long startTime = System.currentTimeMillis();
            System.out.println("[邮件加载] 开始获取邮件，" + timeRangeDesc + "，时间: " + startTime + (customerOnly ? " (仅客户邮件)" : " (所有邮件)"));

            // 优化SQL查询：
            // 1. 直接在SQL中进行排序，避免Java内存排序
            // 2. 使用DISTINCT避免重复
            String sql = "";
            if(customerOnly) {
                sql = "SELECT DISTINCT s.id, s.is_draft, s.subject, s.email_account, s.folder_name, s.from_address, s.to_address, s.sent_date, s.has_attachments, s.is_read, s.is_follow_up, s.follow_up_id, s.follow_up_time, u.name as follow_up_user_name,s.is_important, substr(s.content_html, 1, 500) as content_html, " +
                        "CASE WHEN v.client_id is not null and v.client_id <> '' THEN 1 ELSE 0 END as is_customer_related " +
                        "FROM email_messages s left join jb_user u on s.follow_up_id=u.id, vw_user_email v " +
                        "WHERE s.id=v.emails_id and v.client_id is not null and v.client_id <> '' and v.user_id=? AND s.sent_date >= ?  AND s.sent_date <= ? and ifnull(s.is_delete, 0)<>1 " +
                        "ORDER BY s.sent_date DESC; ";
            }else{
                sql = "SELECT DISTINCT s.id, s.is_draft, s.subject, s.email_account, s.folder_name, s.from_address, s.to_address, s.sent_date, s.has_attachments, s.is_read, s.is_follow_up, s.follow_up_id, s.follow_up_time, u.name as follow_up_user_name,s.is_important, substr(s.content_html, 1, 500) as content_html, " +
                        "CASE WHEN v.client_id is not null and v.client_id <> '' THEN 1 ELSE 0 END as is_customer_related " +
                        "FROM email_messages s left join jb_user u on s.follow_up_id=u.id, vw_user_email v " +
                        "WHERE s.id=v.emails_id and v.user_id=? and (v.email_account_id is null or s.account_id=v.email_account_id) AND s.sent_date >= ?  AND s.sent_date <= ? and ifnull(s.is_delete, 0)<>1 " +
                        "ORDER BY s.sent_date DESC; ";
            }

            System.out.println("[邮件加载] 执行SQL查询: " + sql.replace("?", "'" + userId + "', '" + startDate + "'"));

            // 执行查询
            long beforeQuery = System.currentTimeMillis();
            List<Record> emails = Db.find(sql, userId, startDate, endDate);
            long queryEndTime = System.currentTimeMillis();

            System.out.println("[邮件加载] 查询执行完成，耗时: " + (queryEndTime - beforeQuery) + "ms，查询到邮件数量: " + emails.size());

            // 处理邮件显示名称和日期格式化
            long beforeProcessing = System.currentTimeMillis();
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm");

            for (Record email : emails) {
                // 处理发件人显示名称
                String emailAccount = email.getStr("email_account");
                if (emailAccount != null) {
                    String accountName = emailNameCache.getDisplayName(emailAccount);
                    email.set("account_name", accountName);
                    email.set("accountName", accountName);
                }else{
                    email.set("account_name", "");
                    email.set("accountName", "");
                }

                // 处理发件人显示名称
                String fromAddress = email.getStr("from_address");
                if (fromAddress != null) {
                    String fromName = emailNameCache.getDisplayName(fromAddress);
                    email.set("from_name", fromName);
                    email.set("fromName", fromName);
                }else{
                    email.set("from_name", "");
                    email.set("fromName", "");
                }

                // 处理收件人显示名称
                String toAddress = email.getStr("to_address");
                if (toAddress != null) {
                    String toName = emailNameCache.getDisplayName(toAddress);
                    email.set("to_name", toName);
                    email.set("toName", toName);
                }else{
                    email.set("to_name", "");
                    email.set("toName", "");
                }

                // 格式化日期 - 使用共享的SimpleDateFormat实例提高性能
                Date sentDate = email.getDate("sent_date");
                if (sentDate != null) {
                    String formattedDate = dateFormat.format(sentDate);
                    email.set("sent_date", formattedDate);
                    email.set("sentDate", formattedDate);
                }else{
                    email.set("sent_date", "");
                    email.set("sentDate", "");
                }

                String contentHtml = email.get("content_html");
                if (contentHtml != null) {
                    // 使用正则表达式移除所有<p><br></p>
                    contentHtml = contentHtml.replaceAll("<p><br></p>", "");
                    email.set("content_html", contentHtml);
                }
                
                // 提取纯文本内容的前100个字符
                if (contentHtml != null && !contentHtml.trim().isEmpty()) {
                    // 去除HTML标签，提取纯文本
                    String plainText = extractPlainText(contentHtml);
                    // 截取前100个字符
                    if (plainText.length() > 100) {
                        plainText = plainText.substring(0, 100) + "...";
                    }
                    email.set("content_text", plainText);
                } else {
                    email.set("content_text", "");
                }
            }

            long afterProcessing = System.currentTimeMillis();
            System.out.println("[邮件加载] 处理邮件显示名称和日期完成，耗时: " + (afterProcessing - beforeProcessing) + "ms");

            // 记录总耗时
            long totalTime = System.currentTimeMillis() - startTime;
            System.out.println("[邮件加载] 获取最近邮件完成，总耗时: " + totalTime + "ms");

            renderJsonData(emails);
        } catch (Exception e) {
            renderJsonFail("获取最近邮件失败：" + e.getMessage());
        }
    }
    
    /**
     * 从HTML内容中提取纯文本
     * 使用正则表达式快速去除HTML标签
     * @param html HTML内容
     * @return 纯文本内容
     */
    private String extractPlainText(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }
        
        // 1. 去除script和style标签及其内容
        html = html.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        html = html.replaceAll("(?i)<style[^>]*>.*?</style>", "");
        
        // 2. 去除所有HTML标签
        html = html.replaceAll("<[^>]+>", "");
        
        // 3. 解码HTML实体
        html = html.replace("&nbsp;", " ")
                  .replace("&lt;", "<")
                  .replace("&gt;", ">")
                  .replace("&amp;", "&")
                  .replace("&quot;", "\"")
                  .replace("&#39;", "'");
        
        // 4. 去除多余的空白字符
        html = html.replaceAll("\\s+", " ").trim();
        
        return html;
    }

    /**
     * 获取最近邮件列表（新版本，支持后端过滤）
     * 替代原有的getRecentEmails方法，在SQL层面进行过滤，提升性能
     */
    @UnCheck
    public void getDashboardEmailsWithFilter() {
        try {
            Long userId = JBoltUserKit.getUserId();
            if (userId == null) {
                renderJsonFail("用户未登录");
                return;
            }

            // 获取过滤参数
            Boolean customerOnly = getBoolean("customerOnly", false);
            String startDateStr = getPara("startDate");
            String endDateStr = getPara("endDate");
            Integer days = getInt("days", 1);
            
            // 获取分页参数
            int page = getParaToInt("page", 1);
            int pageSize = getParaToInt("pageSize", 100);
            
            // 获取排序参数
            String sortField = getPara("sortField", "sent_date");
            String sortDirection = getPara("sortDirection", "desc");
            
            // 获取过滤条件
            String senderFilter = getPara("senderFilter");
            String recipientFilter = getPara("recipientFilter");
            String subjectFilter = getPara("subjectFilter");
            String contentFilter = getPara("contentFilter");
            String folderFilter = getPara("folderFilter");

            // 获取快速过滤条件
            String quickFilters = getPara("quickFilters");
            String filterLogic = getPara("filterLogic", "or");

            // 构建查询参数
            Kv params = Kv.by("userId", userId)
                    .set("customerOnly", customerOnly)
                    .set("page", page)
                    .set("pageSize", pageSize)
                    .set("sortField", sortField)
                    .set("sortDirection", sortDirection);

            // 设置时间范围
            if (StrKit.notBlank(startDateStr) && StrKit.notBlank(endDateStr)) {
                params.set("startDate", startDateStr).set("endDate", endDateStr);
            } else {
                params.set("days", days);
            }

            // 设置过滤条件
            if (StrKit.notBlank(senderFilter)) {
                params.set("senderFilter", senderFilter);
            }
            if (StrKit.notBlank(recipientFilter)) {
                params.set("recipientFilter", recipientFilter);
            }
            if (StrKit.notBlank(subjectFilter)) {
                params.set("subjectFilter", subjectFilter);
            }
            if (StrKit.notBlank(contentFilter)) {
                params.set("contentFilter", contentFilter);
            }
            if (StrKit.notBlank(folderFilter)) {
                params.set("folderFilter", folderFilter);
            }
            if (StrKit.notBlank(quickFilters)) {
                params.set("quickFilters", quickFilters);
            }
            if (StrKit.notBlank(filterLogic)) {
                params.set("filterLogic", filterLogic);
            }

            // 调用service获取邮件列表
            Ret result = emailMessagesService.getDashboardEmails(page, pageSize, params);
            
            if (result.isOk()) {
                renderJson(result);
            } else {
                Object msgObj = result.get("msg");
                String msg = msgObj != null ? msgObj.toString() : "获取邮件列表失败";
                renderJsonFail(msg);
            }
            
        } catch (Exception e) {
            renderJsonFail("获取邮件列表失败：" + e.getMessage());
        }
    }
}
