#@jboltLayout()
#define main()
#set(pageId=RandomUtil.random(6))
<div id="dashboard_page_current_id" style="display: none;"></div>
<!--主页面内容显示区域 start-->
<div class="jbolt_page shadow-none bg-transparent" data-key="#(pmkey)" data-require-plugin="morris"
     style="padding:0px 10px;">
    <div class="jbolt_page_content p-0">
        <!-- 紧凑统计栏 -->
        <div class="compact-stats-bar bg-white border-bottom py-2 px-3 mb-3">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="stat-item mr-4">
                        <a href="admin/emailAccount" class="d-flex align-items-center text-decoration-none">
                            <div class="stat-icon bg-primary">
                                <i class="jbicon jb-email text-white"></i>
                            </div>
                            <div class="stat-content ml-2">
                                <span class="stat-number">#(numberOfEmailAccount??)</span>
                                <small class="stat-label d-block">邮箱</small>
                            </div>
                        </a>
                    </div>
                    <div class="stat-item mr-4">
                        <a href="admin/company" class="d-flex align-items-center text-decoration-none">
                            <div class="stat-icon bg-success">
                                <i class="jbicon jb-kehu1 text-white"></i>
                            </div>
                            <div class="stat-content ml-2">
                                <span class="stat-number">#(numberOfCompany??)</span>
                                <small class="stat-label d-block">客户</small>
                            </div>
                        </a>
                    </div>
                    <div class="stat-item mr-4">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-info">
                                <i class="jbicon jb-lianxiren1 text-white"></i>
                            </div>
                            <div class="stat-content ml-2">
                                <span class="stat-number">#(numberOfClient??)</span>
                                <small class="stat-label d-block">联系人</small>
                            </div>
                        </div>
                    </div>
                    <div class="stat-item mr-4">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-danger">
                                <i class="jbicon jb-msg-search text-white"></i>
                            </div>
                            <div class="stat-content ml-2">
                                <span class="stat-number" id="spanNumberOfEmail">#(numberOfEmail??)</span>
                                <small class="stat-label d-block">新邮件</small>
                            </div>
                        </div>
                    </div>
                    <!-- 邮件统计信息 -->
                    <div class="stat-divider mx-3"></div>
                    <div class="email-stats d-flex align-items-center">
                        <div class="email-count-info mr-3">
                            <small class="email-count-label">
                                <select id="emailTimeRange" class="form-control form-control-sm d-inline" style="width: auto; display: inline-block; font-size: 0.8rem; padding: 2px 8px; margin: 0 2px;" onchange="changeEmailTimeRange()">
                                    <option value="1">最近1天</option>
                                    <option value="3">最近3天</option>
                                    <option value="7">最近1周</option>
                                    <option value="15">最近15天</option>
                                    <option value="30">最近1个月</option>
                                    <option value="90">最近3个月</option>
                                    <option value="180">最近半年</option>
                                    <option value="365">最近1年</option>
                                </select>
                                <!-- 自定义时间范围按钮和显示 -->
                                <button id="customTimeBtn" class="btn btn-sm btn-outline-secondary ml-1" onclick="toggleCustomTimeRange()" data-toggle="tooltip" title="自定义时间范围">
                                    <i class="fa fa-calendar"></i>
                                </button>
                                <span id="customTimeDisplay" class="text-muted small ml-1" style="display: none; font-size: 0.7rem;"></span>
                            </small>
                        </div>
                        <div class="custom-control custom-switch custom-switch-sm mr-3" data-toggle="tooltip" title="开启后每5分钟自动刷新邮件列表">
                            <input type="checkbox" class="custom-control-input" id="autoRefreshSwitch" checked>
                            <label class="custom-control-label" for="autoRefreshSwitch">
                                <small>自动刷新</small>
                            </label>
                        </div>
                        <!-- 邮箱账号选择器 -->
                        <div class="mr-3" style="min-width: 200px;">
                            <select class="form-control form-control-sm" id="emailAccountSelector" data-toggle="tooltip" title="选择邮箱账号查看所有邮件">
                                <option value="">选择邮箱账号...</option>
                            </select>
                        </div>
                        <!-- 公司选择器 -->
                        <div class="mr-3" style="min-width: 200px;">
                            <select class="form-control form-control-sm" id="companySelector" data-toggle="tooltip" title="选择公司查看客户邮件">
                                <option value="">选择公司...</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="stat-actions d-flex align-items-center">
                    <button id="refresh-emails-btn" class="btn btn-sm btn-outline-primary mr-2" onclick="refreshEmails()" data-toggle="tooltip" title="刷新邮件列表">
                        <i class="fa fa-refresh"></i>
                    </button>
                    <a href="/admin/emailMessages/composeEmail?_jb_rqtype_=dialog" class="btn btn-sm btn-primary" data-toggle="tooltip" title="撰写新邮件" target="_blank">
                        <i class="fa fa-plus mr-1"></i>新建邮件
                    </a>
                </div>
            </div>
        </div>

        <div class="row mt-0">
            <div class="col-12">
                <div class="card border-0">
                    <div class="card-body p-0">
                        <!-- 搜索过滤区域 -->
                        <div class="p-2 bg-light border-bottom">
                            <div class="row g-1">
                                <div class="col-md-2 col-sm-6 mb-1">
                                    <input type="text" class="form-control form-control-sm" id="filter-sender-#(pageId)" placeholder="发件人筛选(邮箱/姓名)" title="支持按邮箱地址或显示名称筛选">
                                </div>
                                <div class="col-md-2 col-sm-6 mb-1">
                                    <input type="text" class="form-control form-control-sm" id="filter-recipient-#(pageId)" placeholder="收件人筛选(邮箱/姓名)" title="支持按邮箱地址或显示名称筛选">
                                </div>
                                <div class="col-md-2 col-sm-6 mb-1">
                                    <input type="text" class="form-control form-control-sm" id="filter-subject-#(pageId)" placeholder="主题筛选" title="按邮件主题筛选">
                                </div>
                                <div class="col-md-2 col-sm-6 mb-1">
                                    <input type="text" class="form-control form-control-sm" id="filter-content-#(pageId)" placeholder="内容筛选" title="按邮件内容筛选">
                                </div>
                                <div class="col-md-2 col-sm-6 mb-1">
                                    <select class="form-control form-control-sm" id="filter-folder-#(pageId)">
                                        <option value="">选择文件夹...</option>
                                        <option value="inbox">收件箱</option>
                                        <option value="sent">发件箱</option>
                                        <option value="draft">草稿箱</option>
                                    </select>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-1 d-flex flex-wrap">
                                    <button class="btn btn-sm btn-primary mr-1 mb-1" onclick="applyCustomFilters('#(pageId)')">筛选</button>
                                    <!-- 预设过滤下拉选择器 -->
                                    <div class="dropdown mr-1 mb-1">
                                        <button class="btn btn-sm btn-outline-success dropdown-toggle" type="button" id="presetFilterDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="jbicon jb-kehu1 mr-1"></i><span id="currentPresetName">选择预设</span>
                                        </button>
                                        <div class="dropdown-menu" aria-labelledby="presetFilterDropdown" id="presetFilterMenu" style="max-height: 300px; overflow-y: auto;">
                                            <a class="dropdown-item" href="#" onclick="clearAllFiltersAndPreset(); return false;">
                                                <i class="fa fa-times mr-1"></i>清除所有过滤
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <div id="preset-dropdown-list">
                                                <div class="dropdown-item-text text-muted">正在加载预设...</div>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-info mr-1 mb-1" data-toggle="tooltip" title="重置表格列宽为默认值" onclick="resetColumnWidths()">
                                        <i class="fa fa-arrows-h"></i>
                                    </button>
                                    <!-- 快速过滤展开按钮 -->
                                    <button class="btn btn-sm btn-outline-secondary mr-1 mb-1" id="toggle-quick-filters-btn" data-toggle="tooltip" title="展开/收起快速过滤" onclick="toggleQuickFiltersPanel()">
                                        <i class="fa fa-filter mr-1"></i>快速过滤
                                        <i class="fa fa-chevron-down ml-1" id="quick-filters-chevron"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 快速过滤按钮区域 -->
                            <div class="row mt-2" id="quick-filters-panel" style="display: none;">
                                <div class="col-12">
                                    <div class="quick-filters-section bg-light p-2 border-top">
                                        <!-- 快速过滤标题和控制按钮 -->
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0 text-muted">
                                                <i class="fa fa-filter mr-1"></i>快速过滤选项
                                            </h6>
                                            <div class="filter-controls">
                                                <!-- 逻辑运算符切换 -->
                                                <div class="btn-group btn-group-sm mr-2" role="group">
                                                    <button type="button" class="btn btn-outline-secondary" id="filter-logic-and" onclick="toggleFilterLogic('and')">
                                                        <i class="fa fa-check-circle mr-1"></i>AND
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary active" id="filter-logic-or" onclick="toggleFilterLogic('or')">
                                                        <i class="fa fa-circle-o mr-1"></i>OR
                                                    </button>
                                                </div>
                                                <!-- 预设过滤条件 -->
                                                <div class="btn-group btn-group-sm mr-2">
                                                    <button class="btn btn-outline-info dropdown-toggle" type="button" data-toggle="dropdown">
                                                        <i class="fa fa-bookmark mr-1"></i>预设
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right" id="preset-filters-menu">
                                                        <a class="dropdown-item" href="javascript:void(0)" onclick="saveCurrentFiltersAsPreset(); return false;">
                                                            <i class="fa fa-plus mr-1"></i>保存当前过滤条件
                                                        </a>
                                                        <div class="dropdown-divider"></div>
                                                        <div id="preset-filters-list">
                                                            <!-- 预设过滤条件列表将在这里动态生成 -->
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- 清除所有过滤 -->
                                                <button class="btn btn-sm btn-outline-warning" onclick="clearAllQuickFilters()" data-toggle="tooltip" title="清除所有快速过滤条件">
                                                    <i class="fa fa-eraser"></i>
                                                </button>
                                                <!-- 手动刷新预设 -->
                                                <button class="btn btn-sm btn-outline-success ml-1" onclick="loadFilterPresets(true)" data-toggle="tooltip" title="刷新预设列表">
                                                    <i class="fa fa-refresh"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 快速过滤按钮组 -->
                                        <div class="quick-filters-buttons">
                                            <!-- 第一行：收件文件夹 -->
                                            <div class="filter-button-group mb-2">
                                                <span class="filter-group-label">收件文件夹：</span>
                                                <button class="btn btn-sm btn-outline-primary quick-filter-btn"
                                                        data-filter="folder_type" data-value="inbox"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-inbox mr-1"></i>收件箱
                                                </button>
                                                <button class="btn btn-sm btn-outline-success quick-filter-btn"
                                                        data-filter="folder_type" data-value="sent"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-paper-plane mr-1"></i>发件箱
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning quick-filter-btn"
                                                        data-filter="folder_type" data-value="draft"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-edit mr-1"></i>草稿箱
                                                </button>
                                            </div>

                                            <!-- 第二行：邮件方向和状态 -->
                                            <div class="filter-button-group mb-2">
                                                <span class="filter-group-label">邮件方向：</span>
                                                <button class="btn btn-sm btn-outline-success quick-filter-btn"
                                                        data-filter="direction" data-value="incoming"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-arrow-down mr-1"></i>收到
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger quick-filter-btn"
                                                        data-filter="direction" data-value="outgoing"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-arrow-up mr-1"></i>发出
                                                </button>
                                            </div>

                                            <!-- 第三行：阅读状态 -->
                                            <div class="filter-button-group mb-2">
                                                <span class="filter-group-label">阅读状态：</span>
                                                <button class="btn btn-sm btn-outline-primary quick-filter-btn"
                                                        data-filter="read_status" data-value="read"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-envelope-open mr-1"></i>已读
                                                </button>
                                                <button class="btn btn-sm btn-outline-info quick-filter-btn"
                                                        data-filter="read_status" data-value="unread"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-envelope mr-1"></i>未读
                                                </button>
                                            </div>

                                            <!-- 第四行：处理状态 -->
                                            <div class="filter-button-group mb-2">
                                                <span class="filter-group-label">处理状态：</span>
                                                <button class="btn btn-sm btn-outline-warning quick-filter-btn"
                                                        data-filter="follow_status" data-value="followed"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-flag-o mr-1"></i>已处理
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary quick-filter-btn"
                                                        data-filter="follow_status" data-value="not_followed"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-flag mr-1"></i>未处理
                                                </button>
                                            </div>

                                            <!-- 第五行：附件和翻译 -->
                                            <div class="filter-button-group mb-2">
                                                <span class="filter-group-label">特殊标记：</span>
                                                <button class="btn btn-sm btn-outline-dark quick-filter-btn"
                                                        data-filter="has_attachments" data-value="true"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-paperclip mr-1"></i>有附件
                                                </button>
                                                <button class="btn btn-sm btn-outline-purple quick-filter-btn"
                                                        data-filter="has_translation" data-value="true"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-language mr-1"></i>有翻译
                                                </button>
                                                <button class="btn btn-sm btn-outline-cyan quick-filter-btn"
                                                        data-filter="is_important" data-value="true"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="fa fa-star mr-1"></i>重要邮件
                                                </button>
                                            </div>

                                            <!-- 第六行：客户邮件 -->
                                            <div class="filter-button-group mb-2">
                                                <span class="filter-group-label">邮件类型：</span>
                                                <button class="btn btn-sm btn-outline-success quick-filter-btn"
                                                        data-filter="is_customer_email" data-value="true"
                                                        onclick="toggleQuickFilter(this)">
                                                    <i class="jbicon jb-kehu1 mr-1"></i>客户邮件
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 当前过滤状态显示 -->
                                        <div class="current-filters-display mt-2" id="current-filters-display" style="display: none;">
                                            <small class="text-muted">
                                                <i class="fa fa-info-circle mr-1"></i>
                                                当前过滤：<span id="filter-status-text"></span>
                                                （共 <span id="filtered-count">0</span> 封邮件）
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 邮件列表模板 -->
                        #include("/_view/_common/email_list_template.html", pageId = pageId, showPagination = true, showBatchActions = true, enableColumnResize = true)
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--主页面内容显示区域 end-->
#end

#define css()
<!-- 邮件列表模板样式 -->
<link rel="stylesheet" href="/assets/css/email-list-template.css">
<style>
    /* 紧凑统计栏样式 */
    .compact-stats-bar {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        margin: 0 10px;
    }

    .stat-item {
        transition: transform 0.2s ease;
    }

    .stat-item:hover {
        transform: translateY(-1px);
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1;
    }

    .stat-label {
        font-size: 11px;
        color: #6c757d;
        line-height: 1;
        margin-top: 2px;
    }

    .stat-item a {
        color: inherit;
        transition: color 0.2s ease;
    }

    .stat-item a:hover {
        color: #007bff;
        text-decoration: none;
    }

    .stat-item a:hover .stat-number {
        color: #007bff;
    }

    .stat-actions .btn {
        border-radius: 20px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
    }

    .stat-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    /* 分隔线样式 */
    .stat-divider {
        width: 1px;
        height: 30px;
        background-color: #e9ecef;
    }

    /* 自定义时间按钮样式 */
    #customTimeBtn {
        border-radius: 4px;
        transition: all 0.2s ease;
        border-color: #ced4da;
    }

    #customTimeBtn:hover {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    #customTimeBtn.active {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    #customTimeDisplay {
        font-weight: 500;
        color: #495057 !important;
    }

    /* 邮件统计信息样式 */
    .email-count-number {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin-right: 4px;
    }

    .email-count-label {
        font-size: 11px;
        color: #6c757d;
    }

    .email-stats .custom-switch-sm {
        padding-left: 2rem;
    }

    .email-stats .custom-switch-sm .custom-control-label {
        padding-top: 0;
        font-size: 11px;
        color: #6c757d;
    }

    .email-stats .custom-switch-sm .custom-control-label::before {
        height: 0.8rem;
        width: 1.4rem;
        border-radius: 0.4rem;
        left: -2rem;
        top: 0.1rem;
    }

    .email-stats .custom-switch-sm .custom-control-label::after {
        width: calc(0.8rem - 4px);
        height: calc(0.8rem - 4px);
        border-radius: calc(0.8rem / 2);
        left: calc(-2rem + 2px);
        top: calc(0.1rem + 2px);
    }

    .email-stats .custom-switch-sm .custom-control-input:checked ~ .custom-control-label::after {
        transform: translateX(0.6rem);
    }

    .table td {
        max-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-wrap: break-word;
        padding: 0.25rem 0.4rem !important;
        font-size: 0.85rem;
        vertical-align: middle;
        line-height: 1.2;
    }

    .table th {
        font-size: 0.85rem;
        font-weight: 600;
        background-color: #f8f9fa;
        position: relative;
        padding: 0.4rem 0.4rem !important;
        vertical-align: middle;
        line-height: 1.2;
    }

    /* 可调整列宽的表格样式 */
    .resizable-table {
        table-layout: fixed;
        width: 100%;
    }

    /* 可调整列的样式 */
    .resizable-col {
        position: relative;
        user-select: none;
    }

    /* 排序功能相关样式 */
    .sortable-col {
        cursor: pointer;
        user-select: none;
    }

    .sortable-col:hover {
        background-color: #e9ecef !important;
    }

    .sort-icon {
        font-size: 0.75rem;
        transition: color 0.2s;
    }

    .sortable-col:hover .sort-icon {
        color: #007bff !important;
    }

    .sortable-col.sort-asc .sort-icon:before {
        content: "\f0de"; /* fa-sort-up */
        color: #007bff;
    }

    .sortable-col.sort-desc .sort-icon:before {
        content: "\f0dd"; /* fa-sort-down */
        color: #007bff;
    }

    /* 列宽调整手柄 */
    .col-resize-handle {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 5px;
        cursor: col-resize;
        background: transparent;
        z-index: 10;
    }

    .col-resize-handle:hover {
        background: rgba(0, 123, 255, 0.3);
    }

    /* 正在调整时的视觉反馈 */
    .col-resizing {
        cursor: col-resize !important;
        user-select: none !important;
    }

    .col-resizing .col-resize-handle {
        background: rgba(0, 123, 255, 0.5);
    }

    /* 发件人、收件人列样式 - 限制宽度 */
    .table th:nth-child(2),
    .table td:nth-child(2),
    .table th:nth-child(3),
    .table td:nth-child(3) {
        max-width: 160px;
        min-width: 100px;
    }

    /* 时间列固定宽度样式 */
    .table th:nth-child(4),
    .table td:nth-child(4) {
        width: 120px !important;
        min-width: 120px !important;
        max-width: 120px !important;
    }

    /* 复选框列固定宽度样式 */
    .table th:nth-child(1),
    .table td:nth-child(1) {
        width: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
    }

    /* 操作列固定宽度样式 */
    .table th:nth-child(7),
    .table td:nth-child(7) {
        width: 180px !important;
        min-width: 180px !important;
        max-width: 180px !important;
    }

    /* 邮件操作按钮样式优化 */
    .email-actions-cell {
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: initial !important;
    }

    .email-action-btn {
        width: 24px !important;
        height: 24px !important;
        padding: 0 !important;
        margin: 0 1px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 3px !important;
        transition: all 0.2s ease !important;
    }

    .email-action-btn:hover {
        background-color: rgba(0, 123, 255, 0.1) !important;
        transform: scale(1.1) !important;
    }

    .email-action-btn i {
        font-size: 12px !important;
        line-height: 1 !important;
    }

    /* 批量操作区域样式 */
    .batch-operations-bar {
        border-radius: 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 4px solid #007bff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .batch-operations-bar .batch-info {
        font-weight: 500;
        color: #495057;
    }

    .batch-operations-bar .batch-buttons .btn {
        border-radius: 20px;
        font-weight: 500;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .batch-operations-bar .batch-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    /* 复选框样式优化 */
    .custom-control-input:checked ~ .custom-control-label::before {
        background-color: #007bff;
        border-color: #007bff;
    }

    .email-checkbox:checked ~ .custom-control-label::before {
        background-color: #28a745;
        border-color: #28a745;
    }

    /* 移动端批量操作栏样式 */
    .mobile-batch-operations-bar {
        border-radius: 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 4px solid #007bff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        animation: slideDown 0.3s ease-out;
    }

    .mobile-batch-buttons .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 0.75rem;
    }

    /* 内容预览列特殊样式 - 覆盖表格通用样式 */
    .table td.email-preview-cell {
        max-width: none !important;
        white-space: normal !important;
        text-overflow: initial !important;
        overflow: hidden !important; /* 改为hidden，防止重叠 */
        word-wrap: break-word !important;
        vertical-align: top !important;
        padding: 0.25rem 0.4rem !important; /* 减少上下内边距 */
    }

    .unread {
        font-weight: bold;
    }

    /* 邮件内容预览样式 - 单行显示，字体大小与其他列一致 */
    .email-preview {
        color: #666;
        font-size: 0.85rem;
        line-height: 1.2;
        max-height: 1.04rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: left;
        margin: 0;
        padding: 0;
    }

    /* 未读邮件的预览文本样式 */
    .unread .email-preview {
        color: #333;
        font-weight: normal; /* 预览文本不加粗，避免过于突出 */
    }

    /* 空预览内容样式 */
    .email-preview.empty {
        color: #999;
        font-style: italic;
        font-size: 0.75rem;
    }

    /* 预览内容过长时的渐变效果 - 优化样式 */
    .email-preview.long-content::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 30px;
        height: 0.9rem; /* 调整渐变高度 */
        background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.9), white);
        pointer-events: none;
    }

    /* 表格行样式优化 */
    .table tr {
        cursor: pointer;
        height: auto;
    }

    .table tr:hover {
        background-color: #f8f9fa;
    }

    /* 紧凑的表格行 */
    .table-sm th,
    .table-sm td {
        padding: 0.25rem 0.4rem;
    }

    .form-control-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* 刷新按钮样式 */
    #refresh-emails-btn {
        border-radius: 50%;
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    #refresh-emails-btn:hover {
        background-color: #007bff;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    #refresh-emails-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* 自动刷新开关样式 */
    .custom-switch-sm {
        padding-left: 2.25rem;
        font-size: 0.75rem;
    }

    .custom-switch-sm .custom-control-label {
        padding-top: 0.1rem;
        padding-left: 0.5rem;
        padding-bottom: 0.1rem;
    }

    .custom-switch-sm .custom-control-label::before {
        height: 1rem;
        width: 1.75rem;
        border-radius: 0.5rem;
        left: -2.25rem;
        top: 0.15rem;
    }

    .custom-switch-sm .custom-control-label::after {
        width: calc(1rem - 4px);
        height: calc(1rem - 4px);
        border-radius: calc(1rem / 2);
        left: calc(-2.25rem + 2px);
        top: calc(0.15rem + 2px);
    }

    .custom-switch-sm .custom-control-input:checked ~ .custom-control-label::after {
        transform: translateX(0.75rem);
    }

    /* 客户邮件按钮激活状态 */
    .btn-customer-active {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    .email-contact-dialog .nav-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .email-contact-dialog .tab-content {
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.25rem 0.25rem;
    }

    .email-contact-dialog .tab-pane {
        min-height: 250px;
    }

    .email-contact-dialog .form-group {
        margin-bottom: 1rem;
    }

    .email-contact-dialog .form-control {
        font-size: 0.9rem;
    }

    .email-contact-dialog .table td,
    .email-contact-dialog .table th {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .contact-clickable {
        cursor: pointer;
        color: #007bff;
    }

    .contact-clickable:hover {
        text-decoration: underline;
        color: #0056b3;
    }

    td[onclick*="showEmailContactDialog"] {
        cursor: pointer;
        transition: background-color 0.2s;
    }

    td[onclick*="showEmailContactDialog"]:hover {
        background-color: rgba(0, 123, 255, 0.1);
        color: #0056b3;
    }

    /* 分页样式 */
    .email-pagination,
    .email-pagination-top {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 10px 15px;
        border: 1px solid #dee2e6;
    }

    .email-pagination .pagination,
    .email-pagination-top .pagination {
        margin: 0;
    }

    /* 顶部分页控件隐藏/展示功能样式 */
    .email-pagination-toggle {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 8px 15px;
        border: 1px dashed #dee2e6;
        transition: all 0.3s ease;
    }

    .email-pagination-toggle:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .email-pagination-top {
        transition: all 0.3s ease;
    }

    .email-pagination-top.hiding {
        opacity: 0;
        transform: translateY(-10px);
    }

    .email-pagination .page-link,
    .email-pagination-top .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-color: #dee2e6;
    }

    .email-pagination .page-link:hover,
    .email-pagination-top .page-link:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .email-pagination .page-item.active .page-link,
    .email-pagination-top .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }

    .email-pagination .page-item.disabled .page-link,
    .email-pagination-top .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
    }

    .pagination-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .page-size-selector {
        display: flex;
        align-items: center;
    }

    .page-size-selector .form-control {
        width: 80px;
        font-size: 0.875rem;
    }

    /* 移动端响应式设计 */
    @media (max-width: 768px) {
        /* 紧凑统计栏移动端优化 */
        .compact-stats-bar {
            margin: 0 5px;
            padding: 10px;
        }

        .compact-stats-bar .d-flex {
            flex-wrap: wrap;
        }

        .stat-item {
            margin-right: 8px !important;
            margin-bottom: 8px;
        }

        .stat-icon {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }

        .stat-number {
            font-size: 16px;
        }

        .stat-label {
            font-size: 10px;
        }

        /* 邮件统计信息移动端布局 */
        .email-stats {
            flex-direction: column;
            align-items: flex-start !important;
            width: 100%;
            margin-top: 10px;
        }

        .email-count-info {
            margin-bottom: 8px;
            margin-right: 0 !important;
        }

        .email-count-label select {
            font-size: 0.7rem !important;
            padding: 1px 6px !important;
        }

        .custom-control-switch {
            margin-bottom: 8px;
            margin-right: 0 !important;
        }

        /* 操作按钮区域移动端优化 */
        .stat-actions {
            margin-top: 10px;
        }

        .stat-actions .btn {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        /* 筛选区域移动端优化 */
        .card-body .p-2.bg-light {
            padding: 8px !important;
        }

        .card-body .row.g-1 > div {
            margin-bottom: 5px;
        }

        .card-body .form-control-sm {
            font-size: 0.7rem;
            height: calc(1.2em + 0.4rem + 2px);
        }

        .card-body .btn-sm {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        /* 隐藏桌面版邮件表格 */
        .desktop-email-table {
            display: none !important;
        }

        /* 显示移动端邮件列表 */
        .mobile-email-list {
            display: block !important;
        }

        /* 分页控件移动端优化 */
        .email-pagination,
        .email-pagination-top,
        .email-pagination-toggle {
            padding: 8px 10px;
        }

        .email-pagination .d-flex,
        .email-pagination-top .d-flex {
            flex-direction: column;
            gap: 8px;
        }

        .email-pagination .pagination,
        .email-pagination-top .pagination {
            justify-content: center;
            flex-wrap: wrap;
        }

        .email-pagination .page-link,
        .email-pagination-top .page-link {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
        }

        .pagination-info,
        .page-size-selector {
            text-align: center;
            font-size: 0.75rem;
        }
    }

    /* 移动端邮件卡片样式 */
    .mobile-email-list {
        display: none;
    }

    .mobile-email-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 8px;
        padding: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .mobile-email-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
    }

    .mobile-email-card.unread {
        border-left: 4px solid #007bff;
        background: #f8f9ff;
    }

    .mobile-email-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
        position: relative;
    }

    .mobile-email-checkbox {
        position: absolute;
        top: -2px;
        left: -2px;
        z-index: 2;
    }

    .mobile-email-checkbox .custom-control {
        margin: 0;
    }

    .mobile-email-from {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        flex: 1;
        margin-right: 8px;
        margin-left: 18px;
    }

    .mobile-email-time {
        font-size: 0.75rem;
        color: #6c757d;
        white-space: nowrap;
    }

    .mobile-email-subject {
        font-size: 0.85rem;
        color: #495057;
        margin-bottom: 6px;
        font-weight: 500;
        line-height: 1.3;
    }

    .mobile-email-preview {
        font-size: 0.75rem;
        color: #6c757d;
        line-height: 1.4;
        max-height: 2.8rem;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .mobile-email-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.7rem;
    }

    .mobile-email-type {
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .mobile-email-actions {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    .mobile-email-actions .btn {
        padding: 2px 4px;
        font-size: 0.65rem;
        border: none;
        background: none;
        color: #6c757d;
        min-width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3px;
    }

    .mobile-email-actions .btn:hover {
        color: #007bff;
        background: rgba(0, 123, 255, 0.1);
        transform: scale(1.05);
    }

    .mobile-email-badge {
        font-size: 0.6rem;
        padding: 2px 6px;
        border-radius: 10px;
        margin-right: 4px;
    }

    .mobile-email-badge.incoming {
        background-color: #dc3545;
        color: white;
    }

    .mobile-email-badge.outgoing {
        background-color: #28a745;
        color: white;
    }

    .mobile-email-badge.draft {
        background-color: #ffc107;
        color: #212529;
    }

    /* 快速过滤区域样式 */
    .quick-filters-section {
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filter-button-group {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    .filter-group-label {
        font-size: 0.85rem;
        font-weight: 600;
        color: #495057;
        min-width: 80px;
        margin-right: 8px;
    }

    .quick-filter-btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        transition: all 0.2s ease;
        border-width: 1px;
        font-weight: 500;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .quick-filter-btn.active {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
        transform: translateY(-1px);
        font-weight: 600;
    }

    /* 快速过滤按钮激活状态的颜色 */
    .quick-filter-btn[data-filter="folder_type"][data-value="inbox"].active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .quick-filter-btn[data-filter="folder_type"][data-value="sent"].active {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    .quick-filter-btn[data-filter="folder_type"][data-value="draft"].active {
        background-color: #ffc107;
        color: #212529;
        border-color: #ffc107;
    }

    .quick-filter-btn[data-filter="direction"][data-value="incoming"].active {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    .quick-filter-btn[data-filter="direction"][data-value="outgoing"].active {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .quick-filter-btn[data-filter="read_status"][data-value="read"].active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .quick-filter-btn[data-filter="read_status"][data-value="unread"].active {
        background-color: #17a2b8;
        color: white;
        border-color: #17a2b8;
    }

    .quick-filter-btn[data-filter="follow_status"][data-value="followed"].active {
        background-color: #ffc107;
        color: #212529;
        border-color: #ffc107;
    }

    .quick-filter-btn[data-filter="follow_status"][data-value="not_followed"].active {
        background-color: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .quick-filter-btn[data-filter="has_attachments"].active {
        background-color: #343a40;
        color: white;
        border-color: #343a40;
    }

    .quick-filter-btn[data-filter="has_translation"].active {
        background-color: #6f42c1;
        color: white;
        border-color: #6f42c1;
    }

    .quick-filter-btn[data-filter="is_important"].active {
        background-color: #fd7e14;
        color: white;
        border-color: #fd7e14;
    }

    .quick-filter-btn[data-filter="is_customer_email"].active {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    /* 逻辑运算符按钮样式 */
    #filter-logic-and.active {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    #filter-logic-or.active {
        background-color: #17a2b8;
        color: white;
        border-color: #17a2b8;
    }

    /* 当前过滤状态显示样式 */
    .current-filters-display {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 8px 12px;
    }

    /* 预设过滤条件下拉菜单样式 */
    .dropdown-menu {
        max-height: 300px;
        overflow-y: auto;
    }

    .preset-filter-item {
        position: relative;
    }

    .preset-filter-delete {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #dc3545;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .preset-filter-delete:hover {
        color: #c82333;
    }

    /* 移动端快速过滤优化 */
    @media (max-width: 768px) {
        .filter-button-group {
            flex-direction: column;
            align-items: flex-start;
        }

        .filter-group-label {
            min-width: auto;
            margin-bottom: 4px;
            margin-right: 0;
        }

        .quick-filters-buttons .filter-button-group {
            margin-bottom: 12px;
        }

        .quick-filter-btn {
            margin-bottom: 4px;
            font-size: 0.7rem;
            padding: 0.2rem 0.6rem;
        }

        .filter-controls {
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }

        .d-flex.justify-content-between.align-items-center {
            flex-direction: column;
            align-items: flex-start;
        }

        .filter-controls {
            align-self: flex-end;
            margin-top: 8px;
        }
    }

    /* 快速过滤展开按钮样式 */
    #toggle-quick-filters-btn {
        transition: all 0.3s ease;
        position: relative;
    }

    #toggle-quick-filters-btn.btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    #toggle-quick-filters-btn #quick-filters-chevron {
        transition: transform 0.3s ease;
    }

    /* 当有激活的过滤条件时，在按钮上显示小红点 */
    #toggle-quick-filters-btn.has-active-filters::after {
        content: '';
        position: absolute;
        top: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        background-color: #dc3545;
        border-radius: 50%;
        border: 1px solid white;
    }
</style>
#end

#define js()
<!-- 邮件列表模板JavaScript -->
<script type="text/javascript">
    // 设置全局的dashboardPageId变量
    window.dashboardPageId = '#(pageId)' || 'dashboard';

    // 获取邮件数量，返回Promise
    function fetchNumber() {
        return new Promise((resolve, reject) => {
            // 使用jQuery的$.get方法，更兼容框架的Ajax处理
            $.get('/admin/getNumberOfEmail')
                .done(function (data) {
                    console.log('[邮件数量] 响应数据:', data);

                    try {
                        // 处理可能的不同响应格式
                        let number = 0;
                        if (typeof data === 'object' && data.data !== undefined) {
                            number = data.data;
                        } else if (typeof data === 'number') {
                            number = data;
                        } else if (typeof data === 'string') {
                            // 尝试解析字符串
                            const parsed = parseInt(data);
                            if (!isNaN(parsed)) {
                                number = parsed;
                            }
                        }

                        // 更新页面显示
                        const spanElement = document.getElementById('spanNumberOfEmail');
                        if (spanElement) {
                            spanElement.textContent = number;
                        }

                        console.log('[邮件数量] 更新邮件数量:', number);
                        resolve({data: number});

                    } catch (error) {
                        console.error('[邮件数量] 处理响应数据失败:', error);
                        reject(error);
                    }
                })
                .fail(function (xhr, status, error) {
                    console.error('[邮件数量] 请求失败:', error, status, xhr);
                    reject(new Error('获取邮件数量失败: ' + error));
                });
        });
    }

    // 获取最近邮件，返回Promise
    function fetchRecentEmails(customerOnly = false) {
        // 添加默认的pageId，防止未定义
        const pageId = window.dashboardPageId || '#(pageId)' || 'dashboard';
        return new Promise(async (resolve, reject) => {
            try {
                // 记录开始时间
                const startTime = performance.now();
                console.log('[UI] 开始获取最近邮件...' + (customerOnly ? '(仅客户邮件)' : ''), 'pageId:', pageId);

                // 显示加载中的动态效果 - 直接在EmailListTemplate中显示
                showLoadingIndicator(pageId);

                // 禁用刷新按钮
                const refreshBtn = document.getElementById('refresh-emails-btn');
                if (refreshBtn) {
                    refreshBtn.disabled = true;
                    refreshBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                }

                console.log('[UI] 发送请求到服务器...');
                const fetchStartTime = performance.now();

                // 构建请求URL，添加时间戳参数避免缓存
                let url = `/admin/getRecentEmails?_=${Date.now()}`;

                // 获取时间范围设置
                const timeRange = getCurrentTimeRange();
                if (timeRange.days) {
                    url += `&days=${timeRange.days}`;
                } else if (timeRange.startDate && timeRange.endDate) {
                    url += `&startDate=${timeRange.startDate}&endDate=${timeRange.endDate}`;
                }

                if (customerOnly) {
                    url += '&customerOnly=true';
                }

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const fetchEndTime = performance.now();
                console.log(`[UI] 服务器响应耗时: ${(fetchEndTime - fetchStartTime).toFixed(2)}ms`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('[UI] 收到响应:', data);

                if (data.state === 'ok' && data.data) {
                    console.log(`[UI] 成功获取 ${data.data.length} 封邮件，开始渲染...`);

                    // 直接使用EmailListTemplate渲染邮件列表
                    const renderStartTime = performance.now();
                    renderRecentEmails(data.data);
                    const renderEndTime = performance.now();

                    console.log(`[UI] 渲染完成，耗时: ${(renderEndTime - renderStartTime).toFixed(2)}ms`);
                    console.log(`[UI] 整个过程总耗时: ${(renderEndTime - startTime).toFixed(2)}ms`);

                    resolve(data);
                } else {
                    console.warn('[UI] 无效的响应格式或没有数据:', data);
                    // 显示错误信息
                    showErrorMessage(pageId, '获取邮件数据失败，请刷新重试');
                    reject(new Error('Invalid response format or no data'));
                }
            } catch (error) {
                console.error('[UI] 获取最近邮件失败:', error);
                // 显示错误信息
                showErrorMessage(pageId, '获取邮件数据失败，请刷新重试');
                reject(error);
            } finally {
                // 恢复刷新按钮
                const refreshBtn = document.getElementById('refresh-emails-btn');
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.innerHTML = '<i class="fa fa-refresh"></i>';
                }
            }
        });
    }

    // 防止重复请求的标志
    let isEmailFetching = false;
    let lastFetchPromise = null;

    // 新的获取邮件函数，支持后端过滤
    function fetchEmailsWithFilter(page = 1, pageSize = 100, filters = {}) {
        const pageId = window.dashboardPageId || '#(pageId)' || 'dashboard';

        // 防止重复请求
        if (isEmailFetching) {
            console.log('[UI] 邮件正在获取中，返回现有Promise');
            return lastFetchPromise || Promise.resolve({emails: [], total: 0});
        }

        const promise = new Promise(async (resolve, reject) => {
            try {
                isEmailFetching = true;
                const startTime = performance.now();
                console.log('[UI] 开始获取过滤邮件...', 'filters:', filters, 'pageId:', pageId);

                // 显示加载中的动态效果
                showLoadingIndicator(pageId);

                // 构建请求参数
                const params = new URLSearchParams({
                    page: page,
                    pageSize: pageSize,
                    _: Date.now()
                });

                // 添加时间范围参数
                const timeRange = getCurrentTimeRange();
                console.log('[DEBUG] 当前时间范围:', timeRange);
                if (timeRange.days) {
                    params.append('days', timeRange.days);
                    console.log('[DEBUG] 添加days参数:', timeRange.days);
                } else if (timeRange.startDate && timeRange.endDate) {
                    params.append('startDate', timeRange.startDate);
                    params.append('endDate', timeRange.endDate);
                    console.log('[DEBUG] 添加自定义时间范围:', timeRange.startDate, '至', timeRange.endDate);
                }

                // 添加基本过滤参数
                if (filters.customerOnly) {
                    params.append('customerOnly', 'true');
                }

                // 添加文本过滤参数
                if (filters.senderFilter) {
                    params.append('senderFilter', filters.senderFilter);
                }
                if (filters.recipientFilter) {
                    params.append('recipientFilter', filters.recipientFilter);
                }
                if (filters.subjectFilter) {
                    params.append('subjectFilter', filters.subjectFilter);
                }
                if (filters.contentFilter) {
                    params.append('contentFilter', filters.contentFilter);
                }
                if (filters.folderFilter) {
                    params.append('folderFilter', filters.folderFilter);
                }

                // 添加快速过滤参数
                if (filters.quickFilters && Object.keys(filters.quickFilters).length > 0) {
                    params.append('quickFilters', JSON.stringify(filters.quickFilters));
                }
                if (filters.filterLogic) {
                    params.append('filterLogic', filters.filterLogic);
                }

                // 添加排序参数
                if (filters.sortField) {
                    params.append('sortField', filters.sortField);
                }
                if (filters.sortDirection) {
                    params.append('sortDirection', filters.sortDirection);
                }

                // 发送请求到新的过滤接口
                const url = `/admin/getDashboardEmailsWithFilter?${params.toString()}`;
                console.log('[UI] 请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('[UI] 收到过滤响应:', data);

                if (data.state === 'ok' && data.data) {
                    const emails = data.data.list || [];
                    const total = data.data.total || 0;
                    const currentPage = data.data.currentPage || 1;
                    const totalPages = data.data.totalPages || 0;

                    console.log(`[UI] 成功获取 ${emails.length} 封邮件 (总计 ${total} 封)，第 ${currentPage}/${totalPages} 页`);

                    // 保存过滤后的邮件数据
                    window.allEmails = emails;

                    // 保存服务端分页数据
                    window.lastServerPaginationData = {
                        total: total,
                        currentPage: currentPage,
                        totalPages: totalPages,
                        pageSize: pageSize
                    };
                    console.log('[调试] 保存服务端分页数据:', window.lastServerPaginationData);

                    // 使用EmailListTemplate渲染邮件列表
                    renderRecentEmails(emails);

                    // 更新分页信息
                    updatePaginationInfo(currentPage, totalPages, total, pageSize);

                    const totalTime = performance.now() - startTime;
                    console.log(`[UI] 过滤邮件获取完成，总耗时: ${totalTime.toFixed(2)}ms`);

                    resolve({
                        emails: emails,
                        total: total,
                        currentPage: currentPage,
                        totalPages: totalPages
                    });
                } else {
                    console.warn('[UI] 无效的过滤响应格式或没有数据:', data);
                    showErrorMessage(pageId, data.msg || '获取邮件数据失败，请刷新重试');
                    reject(new Error(data.msg || 'Invalid response format or no data'));
                }
            } catch (error) {
                console.error('[UI] 获取过滤邮件失败:', error);
                showErrorMessage(pageId, '获取邮件数据失败，请刷新重试');
                reject(error);
            } finally {
                // 重置请求状态
                isEmailFetching = false;
                lastFetchPromise = null;
            }
        });

        // 保存当前Promise以供重复请求使用
        lastFetchPromise = promise;
        return promise;
    }

    // 更新分页信息显示
    function updatePaginationInfo(currentPage, totalPages, total, pageSize) {
        const pageId = window.dashboardPageId || 'dashboard';
        const paginationContainer = document.getElementById(`emailPaginationContainer_${pageId}`);
        if (paginationContainer) {
            paginationContainer.style.display = total > pageSize ? 'block' : 'none';
            
            const paginationInfo = document.getElementById(`paginationInfo_${pageId}`);
            if (paginationInfo) {
                const start = (currentPage - 1) * pageSize + 1;
                const end = Math.min(currentPage * pageSize, total);
                paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
            }

            // 更新分页按钮
            updatePaginationButtons(currentPage, totalPages);
        }
    }

    // 更新分页按钮
    function updatePaginationButtons(currentPage, totalPages) {
        const pageId = window.dashboardPageId || 'dashboard';
        const pagination = document.getElementById(`emailPagination_${pageId}`);
        if (pagination && totalPages > 1) {
            let paginationHtml = '';

            // 上一页按钮
            const prevDisabled = currentPage === 1 ? 'disabled' : '';
            paginationHtml += `<li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">上一页</a>
            </li>`;

            // 页码按钮
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(1); return false;">1</a>
                </li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                paginationHtml += `<li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                </li>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>`;
                }
                paginationHtml += `<li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a>
                </li>`;
            }

            // 下一页按钮
            const nextDisabled = currentPage === totalPages ? 'disabled' : '';
            paginationHtml += `<li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">下一页</a>
            </li>`;

            pagination.innerHTML = paginationHtml;
        }
    }

    // 切换页码
    function changePage(page) {
        if (page < 1 || !page) return;
        
        // 获取当前的过滤条件
        const filters = getCurrentFilters();

        // 使用新的过滤接口获取指定页的数据
        fetchEmailsWithFilter(page, 100, filters);
    }

    // 获取当前的过滤条件
    function getCurrentFilters() {
        const pageId = window.dashboardPageId || 'dashboard';

        return {
            customerOnly: window.currentCustomerOnly || false,
            senderFilter: document.getElementById(`filter-sender-${pageId}`)?.value || '',
            recipientFilter: document.getElementById(`filter-recipient-${pageId}`)?.value || '',
            subjectFilter: document.getElementById(`filter-subject-${pageId}`)?.value || '',
            contentFilter: document.getElementById(`filter-content-${pageId}`)?.value || '',
            folderFilter: document.getElementById(`filter-folder-${pageId}`)?.value || '',
            quickFilters: window.quickFilters?.active || {},
            filterLogic: window.quickFilters?.logic || 'or',
            sortField: window.dashboardSortState?.currentSortField || 'sent_date',
            sortDirection: window.dashboardSortState?.currentSortDirection || 'desc'
        };
    }

    // 存储所有邮件数据，用于筛选
    window.allEmails = window.allEmails || [];

    // 客户邮件筛选状态
    window.currentCustomerOnly = window.currentCustomerOnly || false;

    // 分页相关配置 - 由EmailListTemplate处理，这里保留供兼容性
    window.emailPagination = {
        pageSize: 100,
        currentPage: 1,
        totalPages: 0,
        filteredEmails: [],
        sortedEmails: []
    };

    // 快速过滤状态管理
    window.quickFilters = window.quickFilters || {
        active: {},             // 当前激活的过滤条件
        logic: 'or',           // 过滤逻辑：'and' 或 'or'
        presets: {}            // 预设过滤条件
    };

    // 排序相关变量 - 由EmailListTemplate处理，这里保留供兼容性
    window.dashboardSortState = window.dashboardSortState || {
        currentSortField: 'sentdate', // 默认按时间排序
        currentSortDirection: 'desc'  // 默认降序（最新的在前）
    };

    // 应用排序 - 现在由EmailListTemplate处理
    function applySorting() {
        // 排序功能现在由EmailListTemplate处理
        return;
    }

    // 更新排序图标 - 现在由EmailListTemplate处理
    function updateSortIcons() {
        // 排序图标更新现在由EmailListTemplate处理
        return;
    }

    // 处理排序点击
    function handleSortClick(field) {
        if (window.dashboardSortState.currentSortField === field) {
            // 如果点击的是当前排序字段，切换排序方向
            window.dashboardSortState.currentSortDirection = window.dashboardSortState.currentSortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击的是新字段，设置为默认排序方向
            window.dashboardSortState.currentSortField = field;
            window.dashboardSortState.currentSortDirection = field === 'sentdate' ? 'desc' : 'asc'; // 时间默认降序，其他升序
        }

        applySorting();
    }

    function renderRecentEmails(emails) {
        console.log('[调试] renderRecentEmails 函数被调用');

        // 清除加载计时器
        if (window.emailLoadingTimer) {
            clearInterval(window.emailLoadingTimer);
            window.emailLoadingTimer = null;
        }

        if (!emails) {
            console.warn('[UI] 没有收到邮件数据');
            return;
        }

        console.log(`[UI] 收到 ${emails.length} 封邮件，开始处理...`);

        // 保存所有邮件数据（用于自定义过滤）
        window.allEmails = emails;

        // 确保EmailListTemplate已经加载并初始化
        const pageId = window.dashboardPageId;
        console.log(`[UI] 当前pageId: ${pageId}`);

        console.log('[调试] 检查EmailListTemplate是否存在:', typeof EmailListTemplate);
        console.log('[调试] window.EmailListTemplate:', window.EmailListTemplate);

        if (typeof EmailListTemplate === 'undefined') {
            console.error('[UI] EmailListTemplate未加载，无法渲染邮件');
            console.log('[调试] 当前页面上的所有script标签:');
            const scripts = document.querySelectorAll('script');
            scripts.forEach((script, index) => {
                console.log(`[调试] Script ${index}:`, script.src || 'inline script');
            });
            return;
        }

        console.log('[调试] EmailListTemplate已加载，类型:', typeof EmailListTemplate);

        if (!EmailListTemplate.instances) {
            EmailListTemplate.instances = {};
            console.log('[调试] 创建EmailListTemplate.instances对象');
        }

        // 检查DOM元素是否存在（延迟检查以确保模板已渲染）
        setTimeout(() => {
            const tableElement = document.getElementById(`email_table_${pageId}`);
            const tbodyElement = document.getElementById(`email_tbody_${pageId}`);
            const mobileElement = document.getElementById(`mobile_email_list_${pageId}`);

            console.log(`[UI] DOM检查 - 表格: ${!!tableElement}, tbody: ${!!tbodyElement}, 移动端: ${!!mobileElement}`);

            if (!tbodyElement) {
                console.error(`[UI] 关键DOM元素缺失 - tbody (ID: email_tbody_${pageId})`);
                // 列出页面上所有的tbody元素
                const allTbodies = document.querySelectorAll('tbody');
                console.log(`[UI] 页面上所有tbody元素:`, allTbodies);
            }
        }, 100);

        if (!EmailListTemplate.instances[pageId]) {
            console.log(`[UI] 初始化EmailListTemplate实例: ${pageId}`);
            console.log(`[UI] 初始化配置:`, {
                showBatchActions: true,
                showPagination: true,
                enableColumnResize: true
            });
            EmailListTemplate.init(pageId, {
                showBatchActions: true,
                showPagination: true,
                enableColumnResize: true
            });
            console.log(`[UI] EmailListTemplate实例初始化完成`);
        } else {
            console.log(`[UI] EmailListTemplate实例已存在: ${pageId}`);
        }

        // 检查是否有服务端分页数据
        const serverPaginationData = window.lastServerPaginationData;
        if (serverPaginationData && serverPaginationData.total !== undefined) {
            console.log('[调试] 使用服务端分页模式渲染邮件');
            console.log('[调试] 服务端分页数据:', serverPaginationData);

            // 使用服务端分页模式渲染，直接传递分页数据
            EmailListTemplate.renderEmailsWithServerPagination(pageId, emails, serverPaginationData);
        } else {
            console.log('[调试] 使用客户端分页模式渲染邮件');
            // 使用客户端分页模式渲染
            EmailListTemplate.renderEmails(pageId, emails);
        }

        // 再次检查渲染后的状态（延迟更长时间以确保渲染完成）
        setTimeout(() => {
            const tbodyAfter = document.getElementById(`email_tbody_${pageId}`);
            const rowCount = tbodyAfter ? tbodyAfter.querySelectorAll('tr').length : 0;
            console.log(`[UI] 渲染后检查 - tbody存在: ${!!tbodyAfter}, 行数: ${rowCount}`);

            if (tbodyAfter && rowCount > 0) {
                console.log(`[UI] ✓ 邮件列表渲染成功！显示 ${rowCount} 行邮件`);
            } else if (tbodyAfter) {
                console.warn(`[UI] ⚠ tbody存在但没有邮件行，可能渲染失败`);
                console.log(`[UI] tbody内容:`, tbodyAfter.innerHTML.substring(0, 200) + '...');
            } else {
                console.error(`[UI] ✗ tbody元素不存在，模板渲染失败`);
            }
        }, 500);
    }


    // Dashboard页面的自定义过滤函数（新版本，使用后端过滤）
    function applyCustomFilters(pageId) {
        // 确保pageId有值
        pageId = pageId || window.dashboardPageId || 'dashboard';

        console.log('[UI] 应用自定义过滤...');

        // 获取当前的过滤条件
        const filters = getCurrentFilters();
        
        // 使用新的后端过滤接口
        fetchEmailsWithFilter(1, 100, filters).then(result => {
            console.log(`[UI] 自定义过滤完成，显示 ${result.emails.length} 封邮件，总计 ${result.total} 封`);
            
            // 更新过滤状态显示
            updateFilterStatusDisplay();
        }).catch(error => {
            console.error('[UI] 自定义过滤失败:', error);
        });
    }

    // 添加回车键触发筛选功能
    document.addEventListener('DOMContentLoaded', function () {
        const pageId = '#(pageId)';
        const filterInputs = [
            document.getElementById(`filter-email-${pageId}`),
            document.getElementById(`filter-folder-${pageId}`),
            document.getElementById(`filter-sender-${pageId}`),
            document.getElementById(`filter-recipient-${pageId}`),
            document.getElementById(`filter-subject-${pageId}`),
            document.getElementById(`filter-content-${pageId}`)
        ];

        filterInputs.forEach(input => {
            if (input) {
                input.addEventListener('keypress', function (e) {
                    if (e.key === 'Enter') {
                        applyFilters(pageId);
                    }
                });
            }
        });
    });

    // 显示加载中的动态效果 - 增强版
    function showLoadingIndicator(pageId) {
        // 确保pageId有值
        pageId = pageId || window.dashboardPageId || 'dashboard';

        // 查找EmailListTemplate的表格tbody
        const tbody = document.getElementById(`email_tbody_${pageId}`);
        if (tbody) {
            showDesktopLoading(tbody);
        }

        // 查找EmailListTemplate的移动端容器
        const mobileContainer = document.getElementById(`mobile_email_list_${pageId}`);
        if (mobileContainer) {
            showMobileLoading(mobileContainer);
        }
    }

    function showDesktopLoading(tbody) {
        // 创建加载中的动画效果 - 更加美观和信息丰富
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-5">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <h5 class="mb-2">正在获取最新邮件</h5>
                        <p class="text-muted small mb-0">首次加载可能需要几秒钟时间，请耐心等待...</p>
                        <div class="progress mt-3" style="width: 50%; height: 6px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <div id="loading-time-counter" class="text-muted small mt-2">已等待 0 秒</div>
                    </div>
                </td>
            </tr>
        `;

        // 添加计时器，显示已等待时间
        let seconds = 0;
        const timeCounter = setInterval(() => {
            seconds++;
            const counterElement = document.getElementById('loading-time-counter');
            if (counterElement) {
                counterElement.textContent = `已等待 ${seconds} 秒`;
            } else {
                clearInterval(timeCounter);
            }
        }, 1000);

        // 将计时器ID存储在window对象中，以便在完成加载后清除
        window.emailLoadingTimer = timeCounter;
    }

    function showMobileLoading(mobileContainer) {
        // 创建移动端加载中的动画效果
        mobileContainer.innerHTML = `
            <div class="text-center py-4">
                <div class="d-flex flex-column justify-content-center align-items-center">
                    <div class="spinner-border text-primary mb-3" style="width: 2rem; height: 2rem;" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <h6 class="mb-2">正在获取最新邮件</h6>
                    <p class="text-muted small mb-0">请稍候...</p>
                    <div id="mobile-loading-time-counter" class="text-muted small mt-2">已等待 0 秒</div>
                </div>
            </div>
        `;

        // 添加计时器，显示已等待时间
        let seconds = 0;
        const timeCounter = setInterval(() => {
            seconds++;
            const counterElement = document.getElementById('mobile-loading-time-counter');
            if (counterElement) {
                counterElement.textContent = `已等待 ${seconds} 秒`;
            } else {
                clearInterval(timeCounter);
            }
        }, 1000);

        // 将计时器ID存储在window对象中，以便在完成加载后清除
        if (!window.emailLoadingTimer) {
            window.emailLoadingTimer = timeCounter;
        }
    }


    // 显示错误信息 - 增强版
    function showErrorMessage(pageId, message) {
        // 确保pageId有值
        pageId = pageId || window.dashboardPageId || 'dashboard';

        // 清除加载计时器
        if (window.emailLoadingTimer) {
            clearInterval(window.emailLoadingTimer);
            window.emailLoadingTimer = null;
        }

        console.log(`[UI] 显示错误信息: ${message}`);

        // 桌面版错误信息 - 查找EmailListTemplate的表格tbody
        const tbody = document.getElementById(`email_tbody_${pageId}`);
        if (tbody) {
            const table = document.getElementById(`email_table_${pageId}`);
            const colCount = table ? table.querySelectorAll('thead th').length : 8;
            tbody.innerHTML = `
                <tr>
                    <td colspan="${colCount}" class="text-center py-5">
                        <div class="d-flex flex-column justify-content-center align-items-center">
                            <div class="text-danger mb-3">
                                <i class="fa fa-exclamation-circle fa-3x"></i>
                            </div>
                            <h5 class="text-danger mb-3">加载失败</h5>
                            <p class="text-muted mb-3">${message}</p>
                            <button class="btn btn-primary" onclick="refreshEmails()">
                                <i class="fa fa-refresh mr-1"></i> 重新加载
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        // 移动端错误信息 - 查找EmailListTemplate的移动端容器
        const mobileContainer = document.getElementById(`mobile_email_list_${pageId}`);
        if (mobileContainer) {
            mobileContainer.innerHTML = `
                <div class="text-center py-4">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <div class="text-danger mb-3">
                            <i class="fa fa-exclamation-circle fa-2x"></i>
                        </div>
                        <h6 class="text-danger mb-3">加载失败</h6>
                        <p class="text-muted small mb-3">${message}</p>
                        <button class="btn btn-primary btn-sm" onclick="refreshEmails()">
                            <i class="fa fa-refresh mr-1"></i> 重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    }

    // 用于存储自动刷新定时器
    window.autoRefreshTimer = window.autoRefreshTimer || null;
    // 用于存储当前预设ID
    window.currentPresetId = window.currentPresetId || null;

    // Dashboard分页函数
    function changeDashboardPage(page) {
        console.log('[UI] 切换到第', page, '页');

        if (page < 1) {
            console.warn('[UI] 页码不能小于1');
            return;
        }

        // 获取当前的过滤条件
        const filters = getCurrentFilters();

        // 使用fetchEmailsWithFilter进行分页
        fetchEmailsWithFilter(page, 100, filters).then(result => {
            console.log(`[UI] 分页完成，显示第${page}页，共 ${result.emails.length} 封邮件，总计 ${result.total} 封`);
        }).catch(error => {
            console.error('[UI] 分页失败:', error);
            LayerMsgBox.alert('分页加载失败，请重试');
        });
    }

    // Dashboard页面大小变更函数
    function changeDashboardPageSize(newPageSize) {
        console.log('[UI] 改变页面大小到', newPageSize);

        if (newPageSize < 1) {
            console.warn('[UI] 页面大小不能小于1');
            return;
        }

        // 获取当前的过滤条件
        const filters = getCurrentFilters();

        // 使用fetchEmailsWithFilter进行分页，回到第1页
        fetchEmailsWithFilter(1, newPageSize, filters).then(result => {
            console.log(`[UI] 页面大小变更完成，每页显示${newPageSize}封邮件，共 ${result.emails.length} 封邮件，总计 ${result.total} 封`);
        }).catch(error => {
            console.error('[UI] 页面大小变更失败:', error);
            LayerMsgBox.alert('页面大小变更失败，请重试');
        });
    }

    // 将函数暴露到全局作用域，供分页按钮调用
    window.changeDashboardPage = changeDashboardPage;
    window.changeDashboardPageSize = changeDashboardPageSize;

    // 初始化自动刷新功能
    function setupAutoRefresh() {
        const autoRefreshSwitch = document.getElementById('autoRefreshSwitch');

        // 从本地存储加载上次的自动刷新设置
        const savedAutoRefresh = localStorage.getItem('autoRefreshEmails');
        if (savedAutoRefresh !== null) {
            autoRefreshSwitch.checked = savedAutoRefresh === 'true';
        }

        // 监听自动刷新开关变化
        autoRefreshSwitch.addEventListener('change', function () {
            // 保存设置到本地存储
            localStorage.setItem('autoRefreshEmails', this.checked);

            // 根据开关状态设置或清除自动刷新定时器
            updateAutoRefreshTimer();

            // 提示用户
            if (this.checked) {
                LayerMsgBox.success('已开启自动刷新，每5分钟刷新一次');
            } else {
                LayerMsgBox.success('已关闭自动刷新');
            }
        });

        // 初始化根据开关状态设置自动刷新
        updateAutoRefreshTimer();
    }

    // 更新自动刷新定时器
    function updateAutoRefreshTimer() {
        const autoRefreshSwitch = document.getElementById('autoRefreshSwitch');

        // 清除现有定时器
        if (window.autoRefreshTimer) {
            clearInterval(window.autoRefreshTimer);
            window.autoRefreshTimer = null;
        }

        // 如果开关打开，设置新的定时器
        if (autoRefreshSwitch && autoRefreshSwitch.checked) {
            window.autoRefreshTimer = setInterval(function () {
                console.log('[自动刷新] 定时器触发，当前预设ID:', window.currentPresetId);
                fetchNumber();

                // 如果有当前预设，保持预设过滤状态
                if (window.currentPresetId) {
                    console.log('[自动刷新] 保持预设过滤状态，预设ID:', window.currentPresetId);
                    // 重新应用当前预设，而不是使用getCurrentFilters
                    applyFilterPreset(window.currentPresetId);
                } else {
                    console.log('[自动刷新] 没有当前预设，使用常规过滤');
                    // 获取当前的过滤条件并使用统一的过滤接口
                    const filters = getCurrentFilters();
                    fetchEmailsWithFilter(1, 100, filters);
                }
            }, 300000); // 5分钟

            console.log('[UI] 已启用自动刷新邮件 (5分钟间隔)');
        } else {
            console.log('[UI] 已禁用自动刷新邮件');
        }
    }

    // 防止重复初始化的标志
    let isDashboardInitialized = false;

    // 统一的初始化入口
    function ensureDashboardInitialized() {
        if (isDashboardInitialized) {
            console.log('[Dashboard] 已经初始化过，跳过重复初始化');
            return;
        }

        console.log('[Dashboard] 开始初始化...');
        isDashboardInitialized = true;
        initializeDashboard();
    }

    // 初始化和定时刷新
    document.addEventListener('DOMContentLoaded', function () {
        console.log('[Dashboard] DOMContentLoaded 事件触发');
        console.log('[调试] 检查EmailListTemplate加载状态:', typeof EmailListTemplate);
        console.log('[调试] window.EmailListTemplate:', window.EmailListTemplate);

        // 检查email-list-template.js是否加载
        const scripts = document.querySelectorAll('script[src*="email-list-template"]');
        console.log('[调试] email-list-template.js脚本标签数量:', scripts.length);
        scripts.forEach((script, index) => {
            console.log(`[调试] Script ${index}:`, script.src, '加载状态:', script.readyState || 'unknown');
        });

        ensureDashboardInitialized();
    });

    // 添加一个统一的初始化函数
    function initializeDashboard() {
        console.log('[Dashboard] 开始统一初始化流程...');

        // 确保设置了全局的dashboardPageId变量
        if (!window.dashboardPageId) {
            window.dashboardPageId = '#(pageId)' || 'dashboard';
        }

        console.log('[Dashboard] 页面加载完成，dashboardPageId:', window.dashboardPageId);

        try {
            // 初始化时间范围选择器
            initTimeRange();

            // 初始化页面数据
            fetchNumber();

            // 先加载预设过滤条件，但不立即应用默认预设
            loadFilterPresets(false); // 传入false表示不立即应用默认预设

            // 页面加载时已经有默认的加载中效果，直接获取邮件
            // 邮件加载完成后会自动应用默认预设
            // 获取当前的过滤条件并使用统一的过滤接口
            const filters = getCurrentFilters();
            fetchEmailsWithFilter(1, 100, filters).then(() => {
                console.log('[Dashboard] 邮件加载完成，开始应用默认预设');
                // 邮件数据加载完成后，应用默认预设
                applyDefaultPresetAfterEmailLoad();
            }).catch(error => {
                console.error('[Dashboard] 邮件加载失败:', error);
            });

            // 初始化刷新按钮的工具提示
            $('#refresh-emails-btn').tooltip();
            $('.custom-control-input').tooltip();
            $('#customTimeBtn').tooltip();

            // 设置自动刷新
            setupAutoRefresh();

            // 定时刷新邮件数量（无论自动刷新是否开启）
            setInterval(fetchNumber, 600000); // 10分钟

            console.log('[Dashboard] 统一初始化流程完成');
        } catch (error) {
            console.error('[Dashboard] 统一初始化流程失败:', error);
        }
    }

    // 对于pjax环境，也可以通过jQuery ready事件触发
    $(document).ready(function () {
        console.log('[Dashboard] jQuery ready 事件触发');
        // 使用统一的初始化入口，防止重复初始化
        ensureDashboardInitialized();
    });

    // 获取当前选择的时间范围
    function getCurrentTimeRange() {
        console.log('[时间范围调试] getCurrentTimeRange 开始');

        // 首先检查是否有自定义时间范围
        const startDate = localStorage.getItem('customStartDate');
        const endDate = localStorage.getItem('customEndDate');
        console.log('[时间范围调试] 自定义时间范围:', { startDate, endDate });

        if (startDate && endDate) {
            console.log('[时间范围调试] 返回自定义时间范围');
            return {
                startDate: startDate,
                endDate: endDate
            };
        }

        // 优先使用select的当前值，然后才是保存的时间范围
        const select = document.getElementById('emailTimeRange');
        console.log('[时间范围调试] select元素:', select);

        let days;
        if (select && select.value) {
            // 如果select存在且有值，优先使用select的值
            days = parseInt(select.value) || 1;
            console.log('[时间范围调试] 使用select的当前值:', select.value, '解析为:', days);
        } else {
            // 如果select不存在或没有值，使用localStorage中保存的值
            const savedTimeRange = localStorage.getItem('emailTimeRange');
            console.log('[时间范围调试] localStorage中的emailTimeRange:', savedTimeRange);

            if (savedTimeRange && savedTimeRange !== 'custom') {
                days = parseInt(savedTimeRange) || 1;
                console.log('[时间范围调试] 使用localStorage中的值:', days);
            } else {
                days = 1; // 默认1天
                console.log('[时间范围调试] 使用默认值:', days);
            }
        }

        console.log('[时间范围调试] 最终返回days:', days);
        return {
            days: days
        };
    }

    // 设置默认时间范围并保存到本地存储
    function initTimeRange() {
        const savedTimeRange = localStorage.getItem('emailTimeRange');
        const select = document.getElementById('emailTimeRange');

        if (select) {
            if (savedTimeRange && savedTimeRange !== 'custom') {
                select.value = savedTimeRange;
            } else {
                select.value = '1'; // 默认最近1天
            }
        }

        // 检查是否有自定义时间范围，如果有则显示
        updateCustomTimeDisplay();
    }

    // 时间范围变化处理函数
    function changeEmailTimeRange() {
        console.log('[时间范围调试] changeEmailTimeRange 被调用');

        const select = document.getElementById('emailTimeRange');
        console.log('[时间范围调试] select元素:', select);
        console.log('[时间范围调试] select.value:', select ? select.value : 'null');

        const timeRange = getCurrentTimeRange();
        console.log('[时间范围调试] getCurrentTimeRange 返回:', timeRange);

        // 保存选择到本地存储
        if (timeRange.days) {
            localStorage.setItem('emailTimeRange', timeRange.days.toString());
            console.log('[时间范围调试] 保存到localStorage:', timeRange.days.toString());
        }

        // 清除自定义时间范围（如果用户选择了预设选项）
        localStorage.removeItem('customStartDate');
        localStorage.removeItem('customEndDate');
        console.log('[时间范围调试] 清除自定义时间范围');

        // 更新自定义时间显示
        updateCustomTimeDisplay();

        // 刷新邮件列表
        console.log('[时间范围调试] 开始刷新邮件列表');
        refreshEmails();

        console.log('[UI] 时间范围已更改为:', timeRange.days ? timeRange.days + '天' : '自定义时间范围');
    }

    // 切换自定义时间范围选择器
    function toggleCustomTimeRange() {
        const startDate = localStorage.getItem('customStartDate');
        const endDate = localStorage.getItem('customEndDate');

        // 根据当前选择的时间范围设置默认日期
        const today = new Date();
        const currentTimeRange = getCurrentTimeRange();

        let defaultStartDate, defaultEndDate;

        if (startDate && endDate) {
            // 如果已有自定义时间，使用保存的值
            defaultStartDate = startDate;
            defaultEndDate = endDate;
        } else {
            // 根据当前选择的时间范围计算默认日期
            defaultEndDate = today.toISOString().split('T')[0];

            if (currentTimeRange.startDate && currentTimeRange.endDate) {
                // 如果当前是自定义状态但没有保存的日期，默认为1天
                defaultStartDate = today.toISOString().split('T')[0];
            } else {
                // 根据选择的天数计算开始日期
                const days = currentTimeRange.days || 1;
                const startDateObj = new Date();
                startDateObj.setDate(today.getDate() - (days - 1)); // -1是因为包含当天
                defaultStartDate = startDateObj.toISOString().split('T')[0];
            }
        }

        // 移除日期范围限制，允许选择任意历史日期
        const todayStr = today.toISOString().split('T')[0];
        // 设置一个合理的最早日期（比如5年前），避免选择过于久远的日期
        const minStartDate = new Date();
        minStartDate.setFullYear(today.getFullYear() - 5);
        const minStartDateStr = minStartDate.toISOString().split('T')[0];

        // 创建自定义时间选择对话框
        const dialogHTML = `
                <div class="custom-time-dialog">
                    <form id="customTimeForm">
                        <div class="form-group">
                            <label for="dialogStartDate">开始日期</label>
                            <input type="date" id="dialogStartDate" class="form-control"
                                   value="${defaultStartDate}"
                                   min="${minStartDateStr}"
                                   max="${todayStr}"
                                   onchange="validateDateRange()">
                            <small class="form-text text-muted">可选择 ${minStartDateStr} 至 ${todayStr} 之间的任意日期</small>
                        </div>
                        <div class="form-group">
                            <label for="dialogEndDate">结束日期</label>
                            <input type="date" id="dialogEndDate" class="form-control"
                                   value="${defaultEndDate}"
                                   min="${minStartDateStr}"
                                   max="${todayStr}"
                                   onchange="validateDateRange()">
                            <small class="form-text text-muted">最晚可选择 ${todayStr}</small>
                        </div>
                        <div class="alert alert-info">
                            <small><i class="fa fa-info-circle"></i>
                                时间范围说明：可选择任意时间范围，结束日期不能早于开始日期
                                <br>当前预设范围：${currentTimeRange.startDate && currentTimeRange.endDate ? '自定义' : '最近' + (currentTimeRange.days || 1) + '天'}
                            </small>
                        </div>
                        <div id="dateRangeWarning" class="alert alert-warning" style="display: none;">
                            <small><i class="fa fa-exclamation-triangle"></i> <span id="warningMessage"></span></small>
                        </div>
                    </form>
                </div>
            `;

        // 使用Layer打开对话框
        layer.open({
            type: 1,
            title: '自定义时间范围',
            area: ['450px', '380px'],
            content: dialogHTML,
            btn: ['确定', '取消', '清除自定义'],
            yes: function (index) {
                applyCustomTimeRange(index);
            },
            btn2: function (index) {
                layer.close(index);
            },
            btn3: function (index) {
                clearCustomTimeRange(index);
            },
            success: function (layero, index) {
                // 对话框打开后，设置日期验证函数
                window.validateDateRange = function () {
                    const startDateInput = document.getElementById('dialogStartDate');
                    const endDateInput = document.getElementById('dialogEndDate');
                    const warningDiv = document.getElementById('dateRangeWarning');
                    const warningMessage = document.getElementById('warningMessage');

                    if (!startDateInput || !endDateInput || !warningDiv || !warningMessage) return;

                    const startDate = new Date(startDateInput.value);
                    const endDate = new Date(endDateInput.value);

                    // 清除之前的警告
                    warningDiv.style.display = 'none';

                    if (startDateInput.value && endDateInput.value) {
                        // 检查日期顺序
                        if (startDate > endDate) {
                            warningMessage.textContent = '开始日期不能晚于结束日期';
                            warningDiv.style.display = 'block';
                            return false;
                        }

                        // 移除15天限制，现在可以选择任意时间范围
                        // 只保留基本的日期范围计算用于显示信息
                        const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1; // +1包含当天
                        console.log(`[时间范围] 选择的时间范围为${daysDiff}天`);

                        // 如果时间范围过长，给出提示但不阻止选择
                        if (daysDiff > 90) {
                            warningMessage.textContent = `选择的时间范围为${daysDiff}天，时间范围较长可能影响查询性能`;
                            warningDiv.style.display = 'block';
                        }
                    }

                    return true;
                };

                // 初始验证
                setTimeout(() => window.validateDateRange(), 100);
            }
        });
    }

    // 应用自定义时间范围
    function applyCustomTimeRange(layerIndex) {
        const startDate = document.getElementById('dialogStartDate').value;
        const endDate = document.getElementById('dialogEndDate').value;

        if (!startDate || !endDate) {
            LayerMsgBox.alert('请选择开始日期和结束日期');
            return;
        }

        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);

        if (startDateObj > endDateObj) {
            LayerMsgBox.alert('开始日期不能晚于结束日期');
            return;
        }

        // 移除15天限制，现在可以选择任意时间范围
        const daysDiff = Math.ceil((endDateObj - startDateObj) / (1000 * 60 * 60 * 24)) + 1; // +1包含当天
        console.log(`[时间范围] 应用自定义时间范围：${daysDiff}天`);

        // 如果时间范围过长，给出提示但不阻止应用
        if (daysDiff > 90) {
            console.warn(`[时间范围] 选择的时间范围较长(${daysDiff}天)，可能影响查询性能`);
        }

        // 保存自定义时间范围到本地存储
        localStorage.setItem('emailTimeRange', 'custom');
        localStorage.setItem('customStartDate', startDate);
        localStorage.setItem('customEndDate', endDate);

        // 更新按钮状态和显示
        updateCustomTimeDisplay();

        // 关闭对话框
        layer.close(layerIndex);

        // 刷新邮件列表
        refreshEmails();

        console.log('[UI] 应用自定义时间范围:', startDate, '至', endDate);
        LayerMsgBox.success(`已设置时间范围：${startDate} 至 ${endDate}`);
    }

    // 清除自定义时间范围
    function clearCustomTimeRange(layerIndex) {
        // 清除自定义时间设置
        localStorage.removeItem('customStartDate');
        localStorage.removeItem('customEndDate');
        localStorage.setItem('emailTimeRange', '1'); // 重置为默认1天

        // 更新select选择器
        const select = document.getElementById('emailTimeRange');
        if (select) {
            select.value = '1';
        }

        // 更新按钮状态和显示
        updateCustomTimeDisplay();

        // 关闭对话框
        layer.close(layerIndex);

        // 刷新邮件列表
        refreshEmails();

        console.log('[UI] 已清除自定义时间范围');
        LayerMsgBox.success('已清除自定义时间范围，恢复为最近1天');
    }

    // 更新自定义时间显示
    function updateCustomTimeDisplay() {
        const customTimeBtn = document.getElementById('customTimeBtn');
        const customTimeDisplay = document.getElementById('customTimeDisplay');
        const startDate = localStorage.getItem('customStartDate');
        const endDate = localStorage.getItem('customEndDate');

        if (startDate && endDate) {
            // 有自定义时间范围
            customTimeBtn.classList.add('active');
            customTimeDisplay.textContent = `${startDate} 至 ${endDate}`;
            customTimeDisplay.style.display = 'inline';
            customTimeBtn.setAttribute('title', `自定义时间范围: ${startDate} 至 ${endDate}`);
        } else {
            // 没有自定义时间范围
            customTimeBtn.classList.remove('active');
            customTimeDisplay.style.display = 'none';
            customTimeBtn.setAttribute('title', '自定义时间范围');
        }

        // 更新tooltip
        $('#customTimeBtn').tooltip('dispose').tooltip();
    }

    // 刷新邮件列表函数
    function refreshEmails() {
        // 添加旋转动画
        const refreshBtn = document.getElementById('refresh-emails-btn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.querySelector('i').classList.add('fa-spin');
        }

        // 显示加载中的动态效果
        const pageId = '#(pageId)';
        showLoadingIndicator(pageId);

        // 获取当前的过滤条件
        const filters = getCurrentFilters();

        // 刷新邮件数量和邮件列表，统一使用fetchEmailsWithFilter
        Promise.all([
            fetchNumber(),
            fetchEmailsWithFilter(1, 100, filters) // 使用统一的过滤接口
        ]).then(() => {
            // 完成后移除旋转动画
            setTimeout(() => {
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.querySelector('i').classList.remove('fa-spin');

                    // 更新刷新时间
                    const now = new Date();
                    const timeStr = now.toLocaleTimeString();
                    $(refreshBtn).attr('title', `上次刷新: ${timeStr}`).tooltip('dispose').tooltip();

                    // 显示成功提示
                    LayerMsgBox.success('邮件列表已刷新');

                    // 存储最后刷新时间
                    window.lastEmailRefreshTime = now;
                }
            }, 500); // 延迟500毫秒以确保动画效果明显
        }).catch(error => {
            console.error('刷新邮件失败:', error);
            // 出错时也移除旋转动画
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.querySelector('i').classList.remove('fa-spin');

                // 显示错误提示
                LayerMsgBox.alert('刷新邮件失败，请稍后重试');
            }
        });
    }

    // 显示邮箱联系人对话框
    function showEmailContactDialog(email, nameEncoded, type) {
        // 解码名称（可能包含特殊字符）
        const name = decodeURIComponent(nameEncoded);

        // 如果邮箱为空，则不执行
        if (!email || email === 'undefined') {
            LayerMsgBox.alert('无效的邮箱地址');
            return;
        }

        // 创建对话框HTML
        const dialogHTML = `
        <div class="email-contact-dialog">
            <ul class="nav nav-tabs" id="emailContactTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="email-alias-tab" data-toggle="tab" href="#email-alias-content" role="tab">邮箱别名管理</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="client-tab" data-toggle="tab" href="#client-content" role="tab">客户联系人管理</a>
                </li>
            </ul>
            <div class="tab-content" id="emailContactTabsContent">
                <div class="tab-pane fade show active" id="email-alias-content" role="tabpanel">
                    <div id="email-alias-loading" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载邮箱别名信息...</p>
                    </div>
                    <div id="email-alias-form" class="mt-3" style="display: none;">
                        <form id="aliasForm">
                            <input type="hidden" id="alias_id" name="id" value="">
                            <div class="form-group">
                                <label for="alias_email">邮箱地址</label>
                                <input type="email" class="form-control" id="alias_email" name="email" value="${email}" readonly>
                            </div>
                            <div class="form-group">
                                <label for="alias_label">显示名称</label>
                                <input type="text" class="form-control" id="alias_label" name="label" value="${name}" placeholder="输入显示名称">
                            </div>
                            <div class="form-group">
                                <label for="alias_remark">备注</label>
                                <textarea class="form-control" id="alias_remark" name="remark" rows="3" placeholder="输入备注信息"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="tab-pane fade" id="client-content" role="tabpanel">
                    <div id="client-loading" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载客户联系人信息...</p>
                    </div>
                    <div id="client-form" class="mt-3" style="display: none;">
                        <form id="clientForm">
                            <input type="hidden" id="client_id" name="id" value="">
                            <div class="form-group">
                                <label for="client_company">所属公司</label>
                                <div class="input-group">
                                    <select class="form-control" id="client_company" name="company_id">
                                        <option value="">正在加载公司列表...</option>
                                    </select>
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" onclick="createNewCompany()">新增公司</button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="client_name">联系人姓名</label>
                                <input type="text" class="form-control" id="client_name" name="name" value="${name}" placeholder="输入联系人姓名">
                            </div>
                            <div class="form-group">
                                <label for="client_email">邮箱地址</label>
                                <input type="email" class="form-control" id="client_email" name="email" value="${email}" readonly>
                            </div>
                            <div class="form-group">
                                <label for="client_mobile">手机号码</label>
                                <input type="text" class="form-control" id="client_mobile" name="mobile" placeholder="输入手机号码">
                            </div>
                            <div class="form-group">
                                <label for="client_position">职位</label>
                                <input type="text" class="form-control" id="client_position" name="position" placeholder="输入职位">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>`;

        // 使用Layer打开对话框
        layer.open({
            type: 1,
            title: `${type === 'from' ? '发件人' : '收件人'}联系信息 - ${email}`,
            area: ['600px', '550px'],
            content: dialogHTML,
            btn: ['保存', '取消'],
            yes: function (index) {
                // 根据当前激活的标签页决定保存哪个表单
                const activeTab = $('#emailContactTabs .nav-link.active').attr('id');

                if (activeTab === 'email-alias-tab') {
                    saveEmailAlias(index);
                } else if (activeTab === 'client-tab') {
                    saveClientInfo(index);
                }
            }
        });

        // 加载邮箱别名信息
        loadEmailAlias(email);

        // 加载客户信息
        loadClientInfo(email);

        // 加载公司列表
        loadCompanyList();
    }

    // 加载邮箱别名信息
    function loadEmailAlias(email) {
        $.get('admin/emailMessages/getEmailAlias', {email: email})
            .done(function (res) {
                $('#email-alias-loading').hide();
                $('#email-alias-form').show();

                if (res.state === 'ok' && res.data) {
                    // 填充表单
                    $('#alias_id').val(res.data.id || '');
                    $('#alias_label').val(res.data.label || '');
                    $('#alias_remark').val(res.data.remark || '');
                }
            })
            .fail(function () {
                $('#email-alias-loading').hide();
                $('#email-alias-form').show();
            });
    }

    // 保存邮箱别名信息
    function saveEmailAlias(layerIndex) {
        const formData = {
            id: $('#alias_id').val(),
            email: $('#alias_email').val(),
            label: $('#alias_label').val(),
            remark: $('#alias_remark').val()
        };

        $.post('admin/emailMessages/saveEmailAlias', formData)
            .done(function (res) {
                if (res.state === 'ok') {
                    layer.close(layerIndex);
                    LayerMsgBox.success('邮箱别名信息保存成功');
                    // 刷新邮件列表以显示更新后的名称
                    refreshEmails();
                } else {
                    LayerMsgBox.alert(res.msg || '保存失败');
                }
            })
            .fail(function () {
                LayerMsgBox.alert('网络错误，保存失败');
            });
    }

    // 加载公司列表
    function loadCompanyList() {
        Ajax.get('admin/company/getCompanyOptions', function (res) {
            if (res.state === 'ok' && res.data) {
                let options = '<option value="">请选择公司</option>';
                res.data.forEach(company => {
                    options += `<option value="${company.id}">${company.name}</option>`;
                });
                $('#client_company').html(options);
            }
        });
    }

    // 加载客户联系人信息
    function loadClientInfo(email) {
        Ajax.get('admin/client/getClientByEmail?email=' + email, function (res) {
            $('#client-loading').hide();
            $('#client-form').show();

            if (res.state === 'ok' && res.data) {
                // 填充表单
                $('#client_id').val(res.data.id || '');
                $('#client_company').val(res.data.company_id || '');
                $('#client_name').val(res.data.name || '');
                $('#client_mobile').val(res.data.mobile || '');
                $('#client_position').val(res.data.position || '');
            }
        });
    }

    // 保存客户联系人信息
    function saveClientInfo(layerIndex) {
        const formData = {
            id: $('#client_id').val(),
            company_id: $('#client_company').val(),
            name: $('#client_name').val(),
            email: $('#client_email').val(),
            mobile: $('#client_mobile').val(),
            position: $('#client_position').val()
        };

        // 验证必填字段
        if (!formData.company_id) {
            LayerMsgBox.alert('请选择所属公司');
            return;
        }

        if (!formData.name) {
            LayerMsgBox.alert('请输入联系人姓名');
            return;
        }

        Ajax.post('admin/client/saveClient', formData, function (res) {
            if (res.state === 'ok') {
                layer.close(layerIndex);
                LayerMsgBox.success('客户联系人信息保存成功');
                // 刷新邮件列表以显示更新后的名称
                refreshEmails();
            } else {
                LayerMsgBox.alert(res.msg || '保存失败');
            }
        });
    }

    // 创建新公司
    function createNewCompany() {
        layer.prompt({
            formType: 0,
            title: '请输入公司名称',
            area: ['400px', '150px']
        }, function (value, index) {
            if (!value.trim()) {
                LayerMsgBox.alert('公司名称不能为空');
                return;
            }

            Ajax.post('admin/company/quickAddCompany', {
                name: value
            }, function (res) {
                if (res.state === 'ok') {
                    layer.close(index);
                    LayerMsgBox.success('公司创建成功');

                    // 重新加载公司列表并选中新创建的公司
                    loadCompanyList();
                    setTimeout(() => {
                        $('#client_company').val(res.data.id);
                    }, 500);
                } else {
                    LayerMsgBox.alert(res.msg || '创建失败');
                }
            });
        });
    }

    // 为发件人/收件人单元格添加样式
    $(document).on('mouseenter', 'td[onclick*="showEmailContactDialog"]', function () {
        $(this).addClass('contact-clickable');
    }).on('mouseleave', 'td[onclick*="showEmailContactDialog"]', function () {
        $(this).removeClass('contact-clickable');
    });

    // 重新定义dashboardPage对象，确保pjax切换时能正确工作
    window.dashboardPage = {
        init: function () {
            console.log('[Dashboard] dashboardPage.init() 被调用');
            // 使用统一的初始化入口，防止重复初始化
            ensureDashboardInitialized();
        },
        destroy: function () {
            console.log('[Dashboard] 开始销毁dashboard页面资源...');

            // 清理自动刷新定时器
            if (window.autoRefreshTimer) {
                clearInterval(window.autoRefreshTimer);
                window.autoRefreshTimer = null;
                console.log('[Dashboard] 已清理自动刷新定时器');
            }

            // 清理加载计时器
            if (window.emailLoadingTimer) {
                clearInterval(window.emailLoadingTimer);
                window.emailLoadingTimer = null;
                console.log('[Dashboard] 已清理邮件加载计时器');
            }

            // 重置初始化状态
            isDashboardInitialized = false;
            isEmailFetching = false;
            lastFetchPromise = null;

            console.log('[Dashboard] dashboard页面资源清理完成');
        },

        // 添加强制重新初始化函数
        forceInit: function () {
            console.log('[Dashboard] 强制重新初始化dashboard页面');
            // 先销毁现有资源
            this.destroy();
            // 延迟初始化
            setTimeout(() => {
                ensureDashboardInitialized();
            }, 200);
        }
    };

    // 暴露全局初始化函数，用于调试和手动触发
    window.initDashboard = function () {
        if (window.dashboardPage && typeof window.dashboardPage.forceInit === 'function') {
            window.dashboardPage.forceInit();
        } else {
            console.warn('[Dashboard] dashboardPage对象未定义，使用直接初始化');
            ensureDashboardInitialized();
        }
    };

    // 列宽调整功能已移除 - 现在由EmailListTemplate处理
    // 已删除以下函数：initColumnResize、setupResizeHandlers、startResize、doResize、
    // stopResize、saveColumnWidths、restoreColumnWidths、cleanupColumnResize

    // 重置列宽到默认值 - 委托给EmailListTemplate
    function resetColumnWidths() {
        const pageId = window.dashboardPageId;
        if (typeof EmailListTemplate !== 'undefined' && EmailListTemplate.resetColumnWidths) {
            EmailListTemplate.resetColumnWidths(pageId);
        } else {
            console.warn('[Column Resize] EmailListTemplate未加载或不支持resetColumnWidths');
        }
    }

    // 分页功能现在由EmailListTemplate处理，已删除相关函数：
    // updatePaginationInfo、generatePaginationButtons、changePage、changePageSize

    // Debug函数 - 检查预设过滤状态
    window.debugPresets = function () {
        console.log('=== 预设过滤调试信息 ===');
        console.log('当前预设ID:', window.currentPresetId);
        console.log('快速过滤状态:', window.quickFilters);

        // 手动加载预设
        console.log('手动触发预设加载...');
        loadFilterPresets();

        // 检查DOM元素
        const presetDropdown = document.getElementById('preset-dropdown-list');
        console.log('预设下拉菜单元素:', presetDropdown);

        const presetPanel = document.getElementById('preset-filters-list');
        console.log('预设面板元素:', presetPanel);

        return {
            currentPresetId: window.currentPresetId,
            quickFilters: window.quickFilters,
            presetDropdown: presetDropdown,
            presetPanel: presetPanel
        };
    };

    // Debug函数 - 检查DOM状态
    window.debugDashboard = function () {
        const pageId = '#(pageId)';
        const expectedTableId = `recent_emails_${pageId}`;

        console.log('[Debug] 预期的表格ID:', expectedTableId);
        console.log('[Debug] window.dashboardPageId:', window.dashboardPageId);

        // 查找表格元素
        const tableElement = document.getElementById(expectedTableId);
        console.log('[Debug] 表格元素:', tableElement);

        // 列出所有包含 recent_emails 的元素
        const allTables = document.querySelectorAll('[id*="recent_emails"]');
        console.log('[Debug] 所有包含recent_emails的元素:', allTables);

        // 检查页面上所有的表格
        const allTableElements = document.querySelectorAll('table');
        console.log('[Debug] 页面上所有的表格元素:', allTableElements);

        return {
            pageId: pageId,
            expectedTableId: expectedTableId,
            tableElement: tableElement,
            allTables: allTables,
            allTableElements: allTableElements
        };
    };

    // ======================== 快速过滤功能 ========================

    // 切换快速过滤面板显示/隐藏
    function toggleQuickFiltersPanel() {
        const panel = document.getElementById('quick-filters-panel');
        const chevron = document.getElementById('quick-filters-chevron');
        const btn = document.getElementById('toggle-quick-filters-btn');

        if (panel.style.display === 'none') {
            // 展开
            panel.style.display = 'block';
            chevron.classList.remove('fa-chevron-down');
            chevron.classList.add('fa-chevron-up');
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-secondary');
            btn.setAttribute('title', '收起快速过滤');

            // 保存展开状态
            localStorage.setItem('quickFiltersExpanded', 'true');

            console.log('[快速过滤] 面板已展开');
        } else {
            // 收起
            panel.style.display = 'none';
            chevron.classList.remove('fa-chevron-up');
            chevron.classList.add('fa-chevron-down');
            btn.classList.remove('btn-secondary');
            btn.classList.add('btn-outline-secondary');
            btn.setAttribute('title', '展开快速过滤');

            // 保存收起状态
            localStorage.setItem('quickFiltersExpanded', 'false');

            console.log('[快速过滤] 面板已收起');
        }

        // 重新初始化tooltip
        $('#toggle-quick-filters-btn').tooltip('dispose').tooltip();
    }

    // 初始化快速过滤面板状态
    function initQuickFiltersPanel() {
        const expanded = localStorage.getItem('quickFiltersExpanded');

        // 默认收起，除非用户之前展开过
        if (expanded === 'true') {
            toggleQuickFiltersPanel();
        }
    }

    // 切换快速过滤按钮状态
    function toggleQuickFilter(button) {
        const filter = button.getAttribute('data-filter');
        const value = button.getAttribute('data-value');
        const isActive = button.classList.contains('active');

        if (isActive) {
            // 取消激活
            button.classList.remove('active');
            delete window.quickFilters.active[filter];
        } else {
            // 激活过滤
            button.classList.add('active');
            window.quickFilters.active[filter] = value;
        }

        // 特殊处理：如果是客户邮件筛选，设置全局标识
        if (filter === 'is_customer_email') {
            window.currentCustomerOnly = !isActive && value === 'true';
            console.log('[快速过滤] 设置客户邮件筛选状态:', window.currentCustomerOnly);
        }

        // 应用快速过滤
        applyQuickFilters();

        // 保存过滤状态到本地存储
        saveQuickFiltersToLocal();

        console.log('[快速过滤] 切换过滤条件:', filter, '=', value, isActive ? '取消' : '激活');
    }

    // 切换过滤逻辑（AND/OR）
    function toggleFilterLogic(logic) {
        window.quickFilters.logic = logic;

        // 更新按钮状态
        const andBtn = document.getElementById('filter-logic-and');
        const orBtn = document.getElementById('filter-logic-or');

        if (logic === 'and') {
            andBtn.classList.add('active');
            orBtn.classList.remove('active');
        } else {
            orBtn.classList.add('active');
            andBtn.classList.remove('active');
        }

        // 重新应用过滤
        applyQuickFilters();

        // 保存到本地存储
        saveQuickFiltersToLocal();

        console.log('[快速过滤] 切换过滤逻辑:', logic);
    }

    // 应用快速过滤（新版本，使用后端过滤）
    function applyQuickFilters() {
        const activeFilters = window.quickFilters.active;
        const logic = window.quickFilters.logic;
        const pageId = window.dashboardPageId;

        console.log('[UI] 应用快速过滤...', activeFilters);

        // 获取当前的过滤条件
        const filters = getCurrentFilters();
        
        // 使用新的后端过滤接口
        fetchEmailsWithFilter(1, 100, filters).then(result => {
            console.log(`[UI] 快速过滤完成，显示 ${result.emails.length} 封邮件，总计 ${result.total} 封`);
            
            // 更新过滤状态显示
            updateFilterStatusDisplay();
        }).catch(error => {
            console.error('[UI] 快速过滤失败:', error);
        });
    }

    // 检查邮件是否符合过滤条件 - 使用JBoltEmailUtil的增强版本
    function checkEmailAgainstFilter(email, filter, value) {
        // 首先尝试使用JBoltEmailUtil的通用过滤器
        if (typeof JBoltEmailUtil !== 'undefined' && JBoltEmailUtil.checkEmailAgainstFilter) {
            const result = JBoltEmailUtil.checkEmailAgainstFilter(email, filter, value);
            if (result !== null && result !== undefined) {
                return result;
            }
        }

        // 处理dashboard特有的过滤条件
        switch (filter) {
            case 'direction':
                if (value === 'incoming') {
                    // 收到的邮件：邮箱账户与发件人地址不同
                    return email.email_account && email.from_address &&
                        email.email_account.toLowerCase() !== email.from_address.toLowerCase();
                } else if (value === 'outgoing') {
                    // 发出的邮件：邮箱账户与发件人地址相同
                    return email.email_account && email.from_address &&
                        email.email_account.toLowerCase() === email.from_address.toLowerCase();
                }
                return false;

            case 'read_status':
                if (value === 'read') {
                    return email.is_read === true || email.is_read === 1;
                } else if (value === 'unread') {
                    return email.is_read === false || email.is_read === 0 || !email.is_read;
                }
                return false;

            case 'follow_status':
                if (value === 'followed') {
                    return email.is_follow_up === true || email.is_follow_up === 1;
                } else if (value === 'not_followed') {
                    return email.is_follow_up === false || email.is_follow_up === 0 || !email.is_follow_up;
                }
                return false;

            case 'has_attachments':
                return email.has_attachments === true || email.has_attachments === 1;

            case 'has_translation':
                return email.has_translation === true || email.has_translation === 1;

            case 'is_important':
                return email.is_important === true || email.is_important === 1 ||
                    email.priority === 'high' || email.importance === 'high';

            case 'is_customer_email':
                // 客户邮件：检查发件人或收件人是否为客户联系人
                return email.is_customer_related === true || email.is_customer_related === 1 ||
                    email.from_is_customer === true || email.from_is_customer === 1 ||
                    email.to_is_customer === true || email.to_is_customer === 1;

            default:
                return false;
        }
    }

    // 更新过滤状态显示
    function updateFilterStatusDisplay() {
        const displayDiv = document.getElementById('current-filters-display');
        const statusText = document.getElementById('filter-status-text');
        const countSpan = document.getElementById('filtered-count');
        const activeFilters = window.quickFilters.active;
        const toggleBtn = document.getElementById('toggle-quick-filters-btn');

        if (Object.keys(activeFilters).length === 0) {
            displayDiv.style.display = 'none';
            // 移除小红点
            if (toggleBtn) {
                toggleBtn.classList.remove('has-active-filters');
            }
            return;
        }

        // 添加小红点
        if (toggleBtn) {
            toggleBtn.classList.add('has-active-filters');
        }

        // 构建过滤状态文本
        const filterTexts = [];
        Object.entries(activeFilters).forEach(([filter, value]) => {
            const text = getFilterDisplayText(filter, value);
            if (text) filterTexts.push(text);
        });

        const logicText = window.quickFilters.logic === 'and' ? ' 且 ' : ' 或 ';
        statusText.textContent = filterTexts.join(logicText);
        countSpan.textContent = window.emailPagination.sortedEmails.length;

        displayDiv.style.display = 'block';
    }

    // 获取过滤条件的显示文本
    function getFilterDisplayText(filter, value) {
        const filterMap = {
            'folder_type': {
                'inbox': '收件箱',
                'sent': '发件箱',
                'draft': '草稿箱'
            },
            'direction': {
                'incoming': '收到',
                'outgoing': '发出'
            },
            'read_status': {
                'read': '已读',
                'unread': '未读'
            },
            'follow_status': {
                'followed': '已处理',
                'not_followed': '未处理'
            },
            'has_attachments': {
                'true': '有附件'
            },
            'has_translation': {
                'true': '有翻译'
            },
            'is_important': {
                'true': '重要邮件'
            },
            'is_customer_email': {
                'true': '客户邮件'
            }
        };

        return filterMap[filter] && filterMap[filter][value] || '';
    }

    // 清除所有快速过滤
    function clearAllQuickFilters() {
        // 清除所有激活的按钮状态
        document.querySelectorAll('.quick-filter-btn.active').forEach(btn => {
            btn.classList.remove('active');
        });

        // 清除过滤状态
        window.quickFilters.active = {};

        // 重新应用过滤（显示所有邮件）
        applyQuickFilters();

        // 保存到本地存储
        saveQuickFiltersToLocal();

        LayerMsgBox.success('已清除所有快速过滤条件');
        console.log('[快速过滤] 已清除所有过滤条件');
    }

    // 保存快速过滤状态到本地存储
    function saveQuickFiltersToLocal() {
        const filterState = {
            active: window.quickFilters.active,
            logic: window.quickFilters.logic
        };
        localStorage.setItem('emailQuickFilters', JSON.stringify(filterState));
        console.log('[快速过滤] 过滤状态已保存到本地存储');
    }

    // 从本地存储加载快速过滤状态
    function loadQuickFiltersFromLocal() {
        try {
            const saved = localStorage.getItem('emailQuickFilters');
            if (saved) {
                const filterState = JSON.parse(saved);
                window.quickFilters.active = filterState.active || {};
                window.quickFilters.logic = filterState.logic || 'or';

                // 恢复按钮状态
                Object.entries(window.quickFilters.active).forEach(([filter, value]) => {
                    const button = document.querySelector(`[data-filter="${filter}"][data-value="${value}"]`);
                    if (button) {
                        button.classList.add('active');
                    }

                    // 特殊处理：如果是客户邮件筛选，设置全局标识
                    if (filter === 'is_customer_email' && value === 'true') {
                        window.currentCustomerOnly = true;
                        console.log('[快速过滤] 恢复客户邮件筛选状态:', window.currentCustomerOnly);
                    }
                });

                // 恢复逻辑按钮状态
                toggleFilterLogic(window.quickFilters.logic);

                console.log('[快速过滤] 已从本地存储恢复过滤状态:', filterState);
            }
        } catch (error) {
            console.warn('[快速过滤] 加载本地过滤状态失败:', error);
        }
    }

    // 保存当前过滤条件为预设
    function saveCurrentFiltersAsPreset() {
        if (Object.keys(window.quickFilters.active).length === 0) {
            LayerMsgBox.alert('当前没有激活的过滤条件，无法保存');
            return;
        }

        layer.prompt({
            formType: 0,
            title: '请输入预设名称',
            area: ['400px', '150px'],
            placeholder: '例如：未读重要邮件'
        }, function (presetName, index) {
            if (!presetName.trim()) {
                LayerMsgBox.alert('预设名称不能为空');
                return;
            }

            // 保存到数据库
            Ajax.post('/admin/emailMessages/saveFilterPreset', {
                name: presetName.trim(),
                filters: JSON.stringify(window.quickFilters.active),
                logic: window.quickFilters.logic
            }, function (res) {
                if (res.state === 'ok') {
                    layer.close(index);
                    LayerMsgBox.success(`预设 "${presetName}" 保存成功`);

                    // 刷新预设列表
                    loadFilterPresets();

                    console.log('[预设过滤] 保存预设成功:', res.data);
                } else {
                    LayerMsgBox.alert(res.msg || '保存预设失败');
                }
            }, function (error) {
                LayerMsgBox.alert('网络错误，保存预设失败');
                console.error('[预设过滤] 保存预设失败:', error);
            });
        });
    }

    // 加载预设过滤条件列表
    function loadFilterPresets(applyDefault = true) {
        const presetsList = document.getElementById('preset-filters-list');
        const presetDropdownList = document.getElementById('preset-dropdown-list');

        // 从数据库加载预设
        console.log('[预设过滤] 开始加载预设列表...');
        const url = '/admin/emailMessages/getFilterPresets';
        console.log('[预设过滤] 请求URL:', url);
        // 使用jQuery的$.get方法替代Ajax.get
        $.get(url)
            .done(function (res) {
                console.log('[预设过滤] 接收到响应:', res);
                if (res.state === 'ok') {
                    const presets = res.data || [];
                    console.log('[预设过滤] 预设数据:', presets);

                    // 验证每个预设的数据完整性
                    presets.forEach((preset, index) => {
                        console.log(`[预设过滤] 预设 ${index}:`, {
                            id: preset.id,
                            name: preset.name,
                            is_default: preset.is_default,
                            filters: preset.filters,
                            logic: preset.logic
                        });
                    });

                    // 更新快速过滤面板中的预设列表
                    if (presetsList) {
                        updatePresetsList(presetsList, presets);
                    }

                    // 更新下拉菜单中的预设列表
                    if (presetDropdownList) {
                        updatePresetsDropdown(presetDropdownList, presets);
                    }

                    // 保存预设数据到全局变量，供后续使用
                    window.allFilterPresets = presets;

                    // 根据参数决定是否应用默认预设
                    if (applyDefault) {
                        applyDefaultPreset(presets);
                    } else {
                        console.log('[预设过滤] 跳过自动应用默认预设，等待邮件加载完成');
                    }
                } else {
                    console.error('[预设过滤] 加载预设列表失败，服务器响应:', res);
                    const errorMsg = '<div class="dropdown-item-text text-danger">加载预设失败: ' + (res.msg || '未知错误') + '</div>';
                    if (presetsList) presetsList.innerHTML = errorMsg;
                    if (presetDropdownList) presetDropdownList.innerHTML = errorMsg;
                }
            })
            .fail(function (xhr, status, error) {
                console.error('[预设过滤] 加载预设列表网络错误:', error, status, xhr);
                const errorMsg = '<div class="dropdown-item-text text-danger">网络错误: ' + (status || '未知错误') + '</div>';
                if (presetsList) presetsList.innerHTML = errorMsg;
                if (presetDropdownList) presetDropdownList.innerHTML = errorMsg;
            });
    }

    // 更新快速过滤面板中的预设列表
    function updatePresetsList(presetsList, presets) {
        if (presets.length === 0) {
            presetsList.innerHTML = '<div class="dropdown-item-text text-muted">暂无预设过滤条件</div>';
            return;
        }

        let html = '';
        presets.forEach(preset => {
            let filters = {};
            try {
                filters = JSON.parse(preset.filters);
            } catch (e) {
                console.warn('[预设过滤] 解析过滤条件失败:', e);
                return;
            }

            const filterTexts = [];
            Object.entries(filters).forEach(([filter, value]) => {
                const text = getFilterDisplayText(filter, value);
                if (text) filterTexts.push(text);
            });

            const logicText = preset.logic === 'and' ? ' 且 ' : ' 或 ';
            const filterDescription = filterTexts.join(logicText);

            // 确保preset.id存在且有效
            if (!preset.id) {
                console.warn('[预设过滤] 跳过无效预设，缺少ID:', preset);
                return;
            }

            // 对名称进行HTML转义，防止特殊字符破坏JavaScript
            const safeName = escapeHtml(preset.name || '');
            const safeDescription = escapeHtml(filterDescription || '');

            html += `
                    <div class="dropdown-item preset-filter-item" style="cursor: pointer;" onclick="applyFilterPreset(${preset.id}); return false;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${safeName}</strong>
                                <br><small class="text-muted">${safeDescription}</small>
                            </div>
                            <i class="fa fa-times preset-filter-delete" onclick="event.stopPropagation(); deleteFilterPreset(${preset.id}); return false;"></i>
                        </div>
                    </div>
                `;
        });

        presetsList.innerHTML = html;
    }

    // 更新下拉菜单中的预设列表
    function updatePresetsDropdown(presetDropdownList, presets) {
        if (presets.length === 0) {
            presetDropdownList.innerHTML = '<div class="dropdown-item-text text-muted">暂无预设过滤条件</div>';
            return;
        }

        let html = '';
        presets.forEach(preset => {
            let filters = {};
            try {
                filters = JSON.parse(preset.filters);
            } catch (e) {
                console.warn('[预设过滤] 解析过滤条件失败:', e);
                return;
            }

            const filterTexts = [];
            Object.entries(filters).forEach(([filter, value]) => {
                const text = getFilterDisplayText(filter, value);
                if (text) filterTexts.push(text);
            });

            const logicText = preset.logic === 'and' ? ' 且 ' : ' 或 ';
            const filterDescription = filterTexts.join(logicText);
            const isDefault = preset.is_default === true || preset.is_default === 1;

            // 确保preset.id存在且有效
            if (!preset.id) {
                console.warn('[预设过滤] 跳过无效预设，缺少ID:', preset);
                return;
            }

            // 对名称进行HTML转义，防止特殊字符破坏JavaScript
            const safeName = escapeHtml(preset.name || '');
            const safeDescription = escapeHtml(filterDescription || '');

            html += `
                    <a class="dropdown-item preset-filter-item" href="#" onclick="selectPresetFromDropdown(${preset.id}, '${safeName}'); return false;">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center">
                                    <strong>${safeName}</strong>
                                    ${isDefault ? '<span class="badge badge-primary badge-sm ml-1">默认</span>' : ''}
                                </div>
                                <small class="text-muted">${safeDescription}</small>
                            </div>
                            <div class="dropdown-actions ml-2">
                                <i class="fa fa-${isDefault ? 'star' : 'star-o'} text-warning" style="cursor: pointer;" onclick="event.stopPropagation(); toggleDefaultPreset(${preset.id}); return false;" title="${isDefault ? '取消默认' : '设为默认'}"></i>
                                <i class="fa fa-times text-danger ml-1" style="cursor: pointer;" onclick="event.stopPropagation(); deleteFilterPreset(${preset.id}); return false;" title="删除"></i>
                            </div>
                        </div>
                    </a>
                `;
        });

        presetDropdownList.innerHTML = html;
    }

    // 应用预设过滤条件
    function applyFilterPreset(presetId) {
        console.log('[预设过滤] 开始应用预设，presetId:', presetId);

        if (!presetId) {
            LayerMsgBox.alert('预设ID不能为空');
            console.error('[预设过滤] presetId为空:', presetId);
            return;
        }

        // 使用jQuery的$.get方法替代Ajax.get
        $.get('/admin/emailMessages/applyFilterPreset', {id: presetId})
            .done(function (res) {
                console.log('[预设过滤] 应用预设响应:', res);

                if (res.state === 'ok') {
                    const preset = res.data;
                    if (!preset) {
                        LayerMsgBox.alert('预设不存在');
                        return;
                    }

                    let filters = {};
                    try {
                        filters = JSON.parse(preset.filters);
                    } catch (e) {
                        LayerMsgBox.alert('预设数据格式错误');
                        console.error('[预设过滤] 解析预设数据失败:', e);
                        return;
                    }

                    // 清除当前过滤状态（但不触发applyQuickFilters）
                    console.log('[预设过滤] 清除当前过滤状态（静默模式）');

                    // 清除所有激活的按钮状态
                    document.querySelectorAll('.quick-filter-btn.active').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // 清除过滤状态（但不调用applyQuickFilters）
                    window.quickFilters.active = {};

                    // 应用预设
                    window.quickFilters.active = {...filters};
                    window.quickFilters.logic = preset.logic;

                    // 恢复按钮状态
                    Object.entries(filters).forEach(([filter, value]) => {
                        const button = document.querySelector(`[data-filter="${filter}"][data-value="${value}"]`);
                        if (button) {
                            button.classList.add('active');
                        }
                    });

                    // 恢复逻辑按钮状态
                    toggleFilterLogic(preset.logic);

                    // 应用过滤
                    console.log('[预设过滤] 开始应用快速过滤，当前过滤条件:', window.quickFilters.active);
                    applyQuickFilters();

                    LayerMsgBox.success(`已应用预设 "${preset.name}"`);
                    console.log('[预设过滤] 应用预设成功:', preset);
                } else {
                    LayerMsgBox.alert(res.msg || '应用预设失败');
                    console.error('[预设过滤] 应用预设失败:', res);
                }
            })
            .fail(function (xhr, status, error) {
                LayerMsgBox.alert('网络错误，应用预设失败');
                console.error('[预设过滤] 应用预设网络错误:', error, status, xhr);
            });
    }

    // 删除预设过滤条件
    function deleteFilterPreset(presetId) {
        layer.confirm('确定要删除这个预设过滤条件吗？', {
            icon: 3,
            title: '确认删除'
        }, function (index) {
            Ajax.post('/admin/emailMessages/deleteFilterPreset', {id: presetId}, function (res) {
                if (res.state === 'ok') {
                    layer.close(index);
                    LayerMsgBox.success('预设删除成功');

                    // 如果删除的是当前应用的预设，清除过滤
                    if (window.currentPresetId === presetId) {
                        clearAllFiltersAndPreset();
                    }

                    // 刷新预设列表
                    loadFilterPresets();

                    console.log('[预设过滤] 删除预设成功:', presetId);
                } else {
                    LayerMsgBox.alert(res.msg || '删除预设失败');
                }
            }, function (error) {
                LayerMsgBox.alert('网络错误，删除预设失败');
                console.error('[预设过滤] 删除预设网络错误:', error);
            });
        });
    }

    // 从下拉菜单选择预设
    function selectPresetFromDropdown(presetId, presetName) {
        applyFilterPreset(presetId);

        // 更新下拉按钮显示
        const currentPresetName = document.getElementById('currentPresetName');
        if (currentPresetName) {
            currentPresetName.textContent = presetName;
        }

        // 更新下拉按钮样式
        const dropdownBtn = document.getElementById('presetFilterDropdown');
        if (dropdownBtn) {
            dropdownBtn.classList.remove('btn-outline-success');
            dropdownBtn.classList.add('btn-success');
        }

        // 保存当前预设ID
        window.currentPresetId = presetId;

        console.log('[预设过滤] 从下拉菜单选择预设:', presetName, presetId);
    }

    // 清除所有过滤和预设
    function clearAllFiltersAndPreset() {
        // 清除快速过滤
        clearAllQuickFilters();

        // 重置下拉按钮
        const currentPresetName = document.getElementById('currentPresetName');
        if (currentPresetName) {
            currentPresetName.textContent = '选择预设';
        }

        const dropdownBtn = document.getElementById('presetFilterDropdown');
        if (dropdownBtn) {
            dropdownBtn.classList.remove('btn-success');
            dropdownBtn.classList.add('btn-outline-success');
        }

        // 清除当前预设ID
        window.currentPresetId = null;

        console.log('[预设过滤] 已清除所有过滤和预设');
    }

    // 切换预设的默认状态
    function toggleDefaultPreset(presetId) {
        console.log('[预设过滤] 开始切换默认预设，presetId:', presetId);

        if (!presetId) {
            LayerMsgBox.alert('预设ID不能为空');
            console.error('[预设过滤] presetId为空:', presetId);
            return;
        }

        // 使用jQuery的$.post方法替代Ajax.post
        $.post('/admin/emailMessages/toggleDefaultPreset', {id: presetId})
            .done(function (res) {
                console.log('[预设过滤] 切换默认预设响应:', res);

                if (res.state === 'ok') {
                    LayerMsgBox.success(res.data.is_default ? '已设为默认预设' : '已取消默认预设');

                    // 刷新预设列表
                    loadFilterPresets();

                    console.log('[预设过滤] 切换默认预设成功:', presetId, res.data.is_default);
                } else {
                    LayerMsgBox.alert(res.msg || '设置默认预设失败');
                    console.error('[预设过滤] 设置默认预设失败:', res);
                }
            })
            .fail(function (xhr, status, error) {
                LayerMsgBox.alert('网络错误，设置默认预设失败');
                console.error('[预设过滤] 设置默认预设网络错误:', error, status, xhr);
            });
    }

    // 应用默认预设
    function applyDefaultPreset(presets) {
        console.log('[预设过滤] 检查是否需要应用默认预设，当前预设ID:', window.currentPresetId);

        // 如果已经有当前预设，不应用默认预设
        if (window.currentPresetId) {
            console.log('[预设过滤] 已有当前预设，跳过默认预设应用');
            return;
        }

        console.log('[预设过滤] 查找默认预设，预设总数:', presets.length);

        // 查找默认预设
        const defaultPreset = presets.find(preset => preset.is_default === true || preset.is_default === 1);
        if (defaultPreset && defaultPreset.id) {
            console.log('[预设过滤] 找到默认预设:', defaultPreset.name, 'ID:', defaultPreset.id);
            selectPresetFromDropdown(defaultPreset.id, defaultPreset.name);
        } else {
            console.log('[预设过滤] 未找到默认预设或默认预设缺少ID');
        }
    }

    // 在邮件加载完成后应用默认预设
    function applyDefaultPresetAfterEmailLoad() {
        console.log('[预设过滤] 邮件加载完成，检查是否需要应用默认预设');
        console.log('[预设过滤] 当前预设ID:', window.currentPresetId);
        console.log('[预设过滤] 可用预设数据:', window.allFilterPresets);

        // 如果已经有当前预设，不应用默认预设
        if (window.currentPresetId) {
            console.log('[预设过滤] 已有当前预设，跳过默认预设应用');
            return;
        }

        // 如果没有预设数据，重新加载
        if (!window.allFilterPresets || window.allFilterPresets.length === 0) {
            console.log('[预设过滤] 没有预设数据，重新加载预设');
            loadFilterPresets(true); // 重新加载并应用默认预设
            return;
        }

        // 查找默认预设
        const defaultPreset = window.allFilterPresets.find(preset => preset.is_default === true || preset.is_default === 1);
        if (defaultPreset && defaultPreset.id) {
            console.log('[预设过滤] 在邮件加载后找到默认预设:', defaultPreset.name, 'ID:', defaultPreset.id);

            // 确保邮件数据已经加载完成
            if (window.allEmails && window.allEmails.length > 0) {
                selectPresetFromDropdown(defaultPreset.id, defaultPreset.name);
                console.log('[预设过滤] 默认预设应用成功');
            } else {
                console.log('[预设过滤] 邮件数据还未准备好，延迟应用默认预设');
                setTimeout(() => {
                    if (window.allEmails && window.allEmails.length > 0) {
                        selectPresetFromDropdown(defaultPreset.id, defaultPreset.name);
                        console.log('[预设过滤] 延迟应用默认预设成功');
                    } else {
                        console.log('[预设过滤] 邮件数据仍未准备好，跳过默认预设应用');
                    }
                }, 1000);
            }
        } else {
            console.log('[预设过滤] 未找到默认预设或默认预设缺少ID');
        }
    }


    // ======================== 快速过滤功能 ========================

    // ======================== 批量操作功能 ========================

    // 批量操作功能已迁移到 jbolt-mine.js 中的 JBoltBatchUtil 和 JBoltEmailBatch
    // 这里只需要配置特定的回调函数

    // 为邮件批量操作配置刷新回调
    $(document).ready(function () {
        // 等待jbolt-mine.js加载完成
        function initBatchOperations() {
            if (typeof JBoltBatchUtil === 'undefined' || typeof JBoltEmailBatch === 'undefined') {
                // 如果批量操作工具还没加载，延迟100ms再试
                setTimeout(initBatchOperations, 100);
                return;
            }

            // 绑定全选复选框事件
            const selectAllCheckbox = document.getElementById('selectAllEmails');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function () {
                    JBoltBatchUtil.toggleSelectAll();
                });
            }

            // 使用事件委托处理动态生成的邮件复选框
            document.addEventListener('change', function (e) {
                if (e.target && e.target.classList.contains('email-checkbox')) {
                    JBoltBatchUtil.updateBatchButtons();
                }
            });

            // 配置批量操作成功后的回调，用于刷新邮件列表
            const emailBatchOptions = {
                onSuccess: function (res) {
                    // 刷新邮件列表
                    refreshEmails();
                }
            };

            // 重写批量操作函数，添加刷新回调
            window.batchMarkRead = function () {
                JBoltEmailBatch.markRead(emailBatchOptions);
            };

            window.batchMarkUnread = function () {
                JBoltEmailBatch.markUnread(emailBatchOptions);
            };

            window.batchFollowUp = function () {
                JBoltEmailBatch.followUp(emailBatchOptions);
            };

            window.batchMarkImportant = function () {
                JBoltEmailBatch.markImportant(emailBatchOptions);
            };

            window.batchDelete = function () {
                JBoltEmailBatch.delete(emailBatchOptions);
            };

            console.log('[Dashboard] 批量操作功能初始化完成');
        }

        // 开始初始化
        initBatchOperations();
    });

    // ======================== 批量操作功能结束 ========================

    // ======================== 邮箱账号和公司选择器 ========================

    // 初始化邮箱账号选择器
    function initEmailAccountSelector() {
        const $selector = $('#emailAccountSelector');
        
        // 使用select2初始化
        $selector.select2({
            placeholder: '选择邮箱账号...',
            allowClear: true,
            minimumInputLength: 0,
            ajax: {
                url: '/admin/emailMessages/getUserEmailAccounts',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term, // 搜索关键词
                        page: params.page || 1
                    };
                },
                processResults: function (data, params) {
                    const results = [];
                    if (data.state === 'ok' && data.data) {
                        data.data.forEach(function(account) {
                            results.push({
                                id: account.email,
                                text: `${account.email_name || account.email} (${account.email})`,
                                data: account
                            });
                        });
                    }
                    return {
                        results: results
                    };
                },
                cache: true
            },
            templateResult: function(option) {
                if (option.loading) {
                    return option.text;
                }
                if (option.data) {
                    return $(`<div>
                        <strong>${option.data.email_name || option.data.email}</strong><br>
                        <small class="text-muted">${option.data.email}</small>
                    </div>`);
                }
                return option.text;
            },
            templateSelection: function(option) {
                return option.data ? (option.data.email_name || option.data.email) : option.text;
            }
        });

        // 选择事件
        $selector.on('select2:select', function (e) {
            const data = e.params.data;
            if (data && data.data) {
                const account = data.data;
                const accountName = account.email_name || account.email;
                const url = `/admin/emailMessages/accountEmails?emailAccount=${encodeURIComponent(account.email)}&accountName=${encodeURIComponent(accountName)}`;
                
                // 在新窗口打开
                window.open(url, '_blank');
                
                // 清空选择
                setTimeout(() => {
                    $selector.val(null).trigger('change');
                }, 300);
            }
        });

        console.log('[Dashboard] 邮箱账号选择器初始化完成');
    }

    // 初始化公司选择器
    function initCompanySelector() {
        const $selector = $('#companySelector');
        
        // 使用select2初始化
        $selector.select2({
            placeholder: '选择公司...',
            allowClear: true,
            minimumInputLength: 0,
            ajax: {
                url: '/admin/emailMessages/getUserCompanies',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term, // 搜索关键词
                        page: params.page || 1
                    };
                },
                processResults: function (data, params) {
                    const results = [];
                    if (data.state === 'ok' && data.data) {
                        data.data.forEach(function(company) {
                            results.push({
                                id: company.id,
                                text: company.nick_name,
                                data: company
                            });
                        });
                    }
                    return {
                        results: results
                    };
                },
                cache: true
            },
            templateResult: function(option) {
                if (option.loading) {
                    return option.text;
                }
                return option.text;
            },
            templateSelection: function(option) {
                return option.text;
            }
        });

        // 选择事件
        $selector.on('select2:select', function (e) {
            const data = e.params.data;
            if (data && data.data) {
                const company = data.data;
                const url = `/admin/emailMessages/companyEmails?companyId=${company.id}&companyName=${encodeURIComponent(company.nick_name)}`;
                
                // 在新窗口打开
                window.open(url, '_blank');
                
                // 清空选择
                setTimeout(() => {
                    $selector.val(null).trigger('change');
                }, 300);
            }
        });

        console.log('[Dashboard] 公司选择器初始化完成');
    }

    // ======================== 邮箱账号和公司选择器结束 ========================

    // 初始化公用预设
    function initDefaultPresets() {
        console.log('[预设过滤] 开始初始化公用预设');

        // 检查并创建"客户邮件"预设
        const customerEmailPreset = {
            name: '客户邮件',
            filters: JSON.stringify({
                'is_customer_email': 'true'
            }),
            logic: 'or',
            isDefault: false
        };

        // 检查并创建"未跟进"预设
        const unfollowedPreset = {
            name: '未跟进',
            filters: JSON.stringify({
                'follow_status': 'not_followed'
            }),
            logic: 'or',
            isDefault: false
        };

        // 创建预设的函数
        function createPresetIfNotExists(preset) {
            return new Promise((resolve, reject) => {
                // 先检查是否已存在
                $.get('/admin/emailMessages/getFilterPresets')
                    .done(function(res) {
                        if (res.state === 'ok') {
                            const existingPresets = res.data || [];
                            const exists = existingPresets.some(p => p.name === preset.name);

                            if (!exists) {
                                // 创建预设
                                Ajax.post('/admin/emailMessages/saveFilterPreset', preset, function(createRes) {
                                    if (createRes.state === 'ok') {
                                        console.log(`[预设过滤] 成功创建预设: ${preset.name}`);
                                        resolve();
                                    } else {
                                        console.warn(`[预设过滤] 创建预设失败: ${preset.name}`, createRes.msg);
                                        resolve(); // 即使失败也继续
                                    }
                                }, function(error) {
                                    console.warn(`[预设过滤] 创建预设网络错误: ${preset.name}`, error);
                                    resolve(); // 即使失败也继续
                                });
                            } else {
                                console.log(`[预设过滤] 预设已存在: ${preset.name}`);
                                resolve();
                            }
                        } else {
                            console.warn('[预设过滤] 获取预设列表失败', res.msg);
                            resolve(); // 即使失败也继续
                        }
                    })
                    .fail(function(error) {
                        console.warn('[预设过滤] 获取预设列表网络错误', error);
                        resolve(); // 即使失败也继续
                    });
            });
        }

        // 依次创建预设
        Promise.all([
            createPresetIfNotExists(customerEmailPreset),
            createPresetIfNotExists(unfollowedPreset)
        ]).then(() => {
            console.log('[预设过滤] 公用预设初始化完成');
            // 重新加载预设列表
            loadFilterPresets(false); // 不自动应用默认预设
        }).catch(error => {
            console.error('[预设过滤] 初始化公用预设失败', error);
        });
    }

    // 等待DOM加载完成后再初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
            console.log('[Dashboard] 底部DOMContentLoaded事件触发');
            setTimeout(() => {
                // 使用统一的初始化入口，防止重复初始化
                ensureDashboardInitialized();
                // 初始化快速过滤
                initQuickFiltersPanel();
                loadQuickFiltersFromLocal();
                // 初始化邮箱账号和公司选择器
                initEmailAccountSelector();
                initCompanySelector();
                // 初始化公用预设
                initDefaultPresets();
            }, 100);
        });
    } else {
        console.log('[Dashboard] DOM已就绪，立即初始化');
        setTimeout(() => {
            // 使用统一的初始化入口，防止重复初始化
            ensureDashboardInitialized();
            // 初始化快速过滤
            initQuickFiltersPanel();
            loadQuickFiltersFromLocal();
            // 初始化邮箱账号和公司选择器
            initEmailAccountSelector();
            initCompanySelector();
            // 初始化公用预设
            initDefaultPresets();
        }, 100);
    }
</script>
#end


