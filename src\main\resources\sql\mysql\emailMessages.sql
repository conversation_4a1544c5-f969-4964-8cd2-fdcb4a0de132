  #sql("findByParams")
    SELECT
        e.*
    FROM 
      email_messages e
    WHERE 1=1
      #if(accountId)
        AND e.account_id = #para(accountId)
      #end
      #if(companyId)
        AND e.id in (SELECT emails_id FROM emails_email WHERE email IN (select email from user_email where company_id = #para(companyId)))
      #end
      #if(email)
        AND (e.from_address LIKE CONCAT('%', #para(email), '%') OR e.from_display LIKE CONCAT('%', #para(email), '%') OR e.recipients LIKE CONCAT('%', #para(email), '%'))
      #end
      #if(subject)
        AND e.subject LIKE CONCAT('%', #para(subject), '%')
      #end
      #if(content)
        AND e.content LIKE CONCAT('%', #para(content), '%')
      #end
    ORDER BY 
      e.sent_date DESC
  #end

  #sql("findDetailById")
    SELECT e.*
    FROM 
      email_messages e
    WHERE 
      e.id = #para(id)
  #end

  #sql("findEmailHistory")
    SELECT 
        e.*,
        #para(email) as target_email
    FROM 
        email_messages e
    WHERE 
        e.is_delete = 0
        AND (
            e.from_address = #para(email)
            OR e.from_display LIKE CONCAT('%', #para(email), '%')
            OR e.to_address LIKE CONCAT('%', #para(email), '%')
            OR e.to_display LIKE CONCAT('%', #para(email), '%')
            OR e.cc_address LIKE CONCAT('%', #para(email), '%')
        )
        #if(typeFilter)
            #if(typeFilter == "received")
                AND e.from_address != #para(email)
            #elseif(typeFilter == "sent")  
                AND e.from_address = #para(email)
            #end
        #end
        #if(readStatusFilter)
            #if(readStatusFilter == "read")
                AND e.is_read = 1
            #elseif(readStatusFilter == "unread")
                AND e.is_read = 0
            #end
        #end
        #if(subjectSearch)
            AND e.subject LIKE #para(subjectSearch)
        #end
    ORDER BY 
        #if(sortField == "subject")
            e.subject #para(sortDirection)
        #else
            e.sent_date #para(sortDirection)
        #end
  #end
